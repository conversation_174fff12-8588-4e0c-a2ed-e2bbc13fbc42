# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

def get_version_code
  require 'yaml'
  pubspec = YAML.load_file('../pubspec.yaml')
  pubspec['version'].split('+').last
end

def get_version_name
  require 'yaml'
  pubspec = YAML.load_file('../pubspec.yaml')
  pubspec['version'].split('+').first
end

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Build and deploy to internal track for staging flavor"
  lane :staging do
    sh("flutter build appbundle --flavor staging --release")

    lane_context[SharedValues::GRADLE_AAB_OUTPUT_PATH] = "build/app/outputs/bundle/stagingRelease/app-staging-release.aab"

    supply(
      track: "internal",
      aab: lane_context[SharedValues::GRADLE_AAB_OUTPUT_PATH],
      package_name: "com.wd.e2ee.chat.staging",
      version_code: get_version_code,
      version_name: get_version_name,
      skip_upload_changelogs: true
    )
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store
  end
end
