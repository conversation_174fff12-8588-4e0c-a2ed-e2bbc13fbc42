{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98943a067b7c2a9ccd0bf807dac22c0ed7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d7c6b7e69dff06d50558dc1b448365cf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98943a067b7c2a9ccd0bf807dac22c0ed7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98677ff757229d524389b3ee9fb14b7819", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98943a067b7c2a9ccd0bf807dac22c0ed7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987a7a07491865003ac4c31553e23e4e93", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98943a067b7c2a9ccd0bf807dac22c0ed7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f11e93685416dcae29ab0ab758e0e6c5", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98569a156d5f4da9edb3bee76e8637120c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981ffb8a8a229c8a4af5c2747592e503f5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98569a156d5f4da9edb3bee76e8637120c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9830176a3c997a94f11db29e009677e756", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98569a156d5f4da9edb3bee76e8637120c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f7d15abcf030da96cd42e646dba2acc", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98569a156d5f4da9edb3bee76e8637120c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98454010eeba0203cdbccb070f2e74ccf7", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98569a156d5f4da9edb3bee76e8637120c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986fa8764b08c89ff3448de4800ef767d8", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98569a156d5f4da9edb3bee76e8637120c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982e04d2d647b87706325c5dacd31b5ab6", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98569a156d5f4da9edb3bee76e8637120c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fface666531654702588da23794c1b54", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98569a156d5f4da9edb3bee76e8637120c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a4a24734128739965126ff3f60fe4e5f", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7d996292e59a8a2695ef931098a872c", "guid": "bfdfe7dc352907fc980b868725387e98f32531d64f7f2b08b74ed9c164f6bfb5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c7253fe3a91c2c8b75b258823df07e9", "guid": "bfdfe7dc352907fc980b868725387e9878b6e7176fc2a97a01107362d7778b26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811c982cabf58393b343896668b7ea943", "guid": "bfdfe7dc352907fc980b868725387e98a43756194e82627c38cd2ae5f085a943", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983abcf6b3cd038c6bebfd7705ee1b698d", "guid": "bfdfe7dc352907fc980b868725387e98ec0bb3b17645d019e899300740e36463", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c2e64b7dc764a002f8bea8d9f407985", "guid": "bfdfe7dc352907fc980b868725387e988400290b2973be5d23a0b4c370999493", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c1a5557bb6c1cec6d67e656dfe35a5f", "guid": "bfdfe7dc352907fc980b868725387e98ad957f628774b2cb6ae44731c7e56840", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed7a90d6e24f46e8f0d58c26b5d35a5", "guid": "bfdfe7dc352907fc980b868725387e98428d35ee9df23d1517279b937ff8f55e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98061d558224e901d6143e2299faddfa09", "guid": "bfdfe7dc352907fc980b868725387e984fab68cb18f1fa8b2ddc3dfcccfffe5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837acdea664f7a7e64304efc36c60df84", "guid": "bfdfe7dc352907fc980b868725387e98ad00d7590d55b6d7d6e6843243b79974", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855002c7722772e8eb3ddc3e15eba2558", "guid": "bfdfe7dc352907fc980b868725387e985ef5fa31def7046ae059d2d23459faa0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861bf1ad086eb63d0db9232c982fcd31c", "guid": "bfdfe7dc352907fc980b868725387e98fb754f5f54bc23ae47dfabb424528b9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8fbf60b47786f6b8961df04c45192ed", "guid": "bfdfe7dc352907fc980b868725387e9882272f6ffead2959b75e2486d22ce20f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983171cd32b87c518295af454c3d91e30e", "guid": "bfdfe7dc352907fc980b868725387e98330853eaf22379db30d9a9671076dd87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e1e0f2d974f7ca62bad79ff7468a230", "guid": "bfdfe7dc352907fc980b868725387e9832524c36607ea686267055d8a851920a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987edf0e4c79e292a70945fc0ed0e547ff", "guid": "bfdfe7dc352907fc980b868725387e98a98fa9f1869f8fb86489b3a76b82b0d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845d7c8d8668fcab780fe2359ab15b911", "guid": "bfdfe7dc352907fc980b868725387e98f10bfc2f22edd8a433bc56d3a03939a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98facff02b976a5a6cd120cb11773356bd", "guid": "bfdfe7dc352907fc980b868725387e98f91748e2b6558fd06ad90154df4a0adc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98096128e3428fcb109ee103a5d58bfc7b", "guid": "bfdfe7dc352907fc980b868725387e989bfd0e84bfd83951b75d59d0f2bce355", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2ac4d865be542db26f62991ba394064", "guid": "bfdfe7dc352907fc980b868725387e988d6dadd26a241fe0354d1823e3fad746", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f105050aebe9415accf8d31d8d3c76", "guid": "bfdfe7dc352907fc980b868725387e98d207ed582ca732813ed3f1620714b211", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d7dd9c5a3eef2ce976e76e64186fd0", "guid": "bfdfe7dc352907fc980b868725387e98d5de0c245939120a207299a2dcd291e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98988fba0e46a7e78701132ee63effd103", "guid": "bfdfe7dc352907fc980b868725387e9884841cedfa6ba9991c0eeff2327cd687", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ca8e4982f33bc9009e5baae21b0ca14a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983ca732504fd5d9e27680d51c7a936cd3", "guid": "bfdfe7dc352907fc980b868725387e98940d756ef116ea1a5c29c20baa0cd132"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838c2a3a10dd031924dacfa168b9f61d0", "guid": "bfdfe7dc352907fc980b868725387e98ab7a10d61690d67582a0c6bd8d0e50cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849108089814abba391a81e9411e2828c", "guid": "bfdfe7dc352907fc980b868725387e989dcfda17d577d365ba16bb8eecc1ae72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5830516b67fadda778a39941c8c1ac5", "guid": "bfdfe7dc352907fc980b868725387e98cfdbb7d5c40b94a55cca529948105ef7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840e591894d5845728aacd1747742188b", "guid": "bfdfe7dc352907fc980b868725387e98abd4b2dc1e38d379ee38ecf97a10111c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883f30bfaa2290f8e5e2caeb000e82cf9", "guid": "bfdfe7dc352907fc980b868725387e9839950f98cf6beb5a71011a04d7dc0130"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22642a917c24f08210fef12169d2952", "guid": "bfdfe7dc352907fc980b868725387e989e3e19933ccd61e0888448fcc3c14bc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d370f44f5e3555fdfc5ea62c10afb41", "guid": "bfdfe7dc352907fc980b868725387e98213500b1326f292871dfc6602dc24324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b95fa1f48027358180b15b51933b956d", "guid": "bfdfe7dc352907fc980b868725387e98d0bfd34f095747921823d1aa675db619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d038d3cfcf3041074f12aee1ebf218be", "guid": "bfdfe7dc352907fc980b868725387e987bfd7c1fb6f40fbe830cd01368160778"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9d172a377d663ab24c847dedcd9a794", "guid": "bfdfe7dc352907fc980b868725387e98c4be84786ca8b7ea2f55387450aeea6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986da95934497a08750b0c6432c3b6f299", "guid": "bfdfe7dc352907fc980b868725387e98ca2c9900725cfa2ec00e4eb03da95e37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98652dda273ae96762ad248ad9c1e3dbe3", "guid": "bfdfe7dc352907fc980b868725387e98983a74ca128e070d31e746216daeb399"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873fcc5ea91752090d17668e7ac77bf17", "guid": "bfdfe7dc352907fc980b868725387e9818e4934a60b8a1254c6b71d586515dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98010d8d93d8ff3eb6136cb9a074e92e0a", "guid": "bfdfe7dc352907fc980b868725387e98f8af80eea7c90804cda6ca9fd2b8c0fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa19f79dd11bc5114f533326f348f767", "guid": "bfdfe7dc352907fc980b868725387e982edb521cf8a3374a234de6894c64676f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b98d2b76b6cfa7a90e5cd647722ff25", "guid": "bfdfe7dc352907fc980b868725387e98984ca772985a2de49f20fc827492bffc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fcb45d977e10a790b2817d64b3ff936", "guid": "bfdfe7dc352907fc980b868725387e981d991bdaee0793ea9a2a4fb830537406"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98669d64f8fa1f8d074cbaf11a8ca83482", "guid": "bfdfe7dc352907fc980b868725387e9853048c6bb6e8dba5fde53f0c186bb8ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7b5dde46f19df1a69828ff7f45f4b4b", "guid": "bfdfe7dc352907fc980b868725387e98ddc2d26c0606d73f146074ba7e0ddae9"}], "guid": "bfdfe7dc352907fc980b868725387e981f03a540c066796b65e57497ee335c0c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e27d661740f9dabd2aa31605d055f69", "guid": "bfdfe7dc352907fc980b868725387e98777a9f7a80937e2761f1f145d3e40c9e"}], "guid": "bfdfe7dc352907fc980b868725387e98cc3469542c749fc9a6a1f57dcfa51bce", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987ff2d390369bba657f77a5b7c51addcc", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e982795c56fd0155f32e553d0038bd0b4fb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 1}], "type": "standard"}