{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824517ea1d12f20f9934d1132436aa264", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98da7bbc64ca663f18f4c918f1de8563ef", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824517ea1d12f20f9934d1132436aa264", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98545eea59cf3e04f81e78f43b94b68a57", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824517ea1d12f20f9934d1132436aa264", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9839a96a16b81d86bed29754bf17e01553", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824517ea1d12f20f9934d1132436aa264", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9884678b85514ae31b12e80f0095f973ed", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983953c72b746f31377569a4e0c96054d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a94051d3a20621982e33cf6f35bbce41", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983953c72b746f31377569a4e0c96054d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983d1d5a0a02d8f1ba4706835adb47337d", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983953c72b746f31377569a4e0c96054d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c871c7d691d76e12833c9eca322a0ff0", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983953c72b746f31377569a4e0c96054d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a464a5ab6c852dca20d7f7e2aec02b6a", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983953c72b746f31377569a4e0c96054d8", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e985108639aa76b67d41339af3850b0d98d", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983953c72b746f31377569a4e0c96054d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9833c8ed976c366036a4e5d9d623741713", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983953c72b746f31377569a4e0c96054d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989c9d3e77f959b6929c0de451f1d1779f", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983953c72b746f31377569a4e0c96054d8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/isar_flutter_libs", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "isar_flutter_libs", "INFOPLIST_FILE": "Target Support Files/isar_flutter_libs/ResourceBundle-isar_flutter_libs_apple_privacy-isar_flutter_libs-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "isar_flutter_libs_apple_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98ad26ca33dd8d3f03b28ddd7f1e412c95", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985f803ce26f5d7116901aea36ca7f2d0d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c3dc4326791257e30dc47a2ffa5b2e4d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9809fec76696fce942a2e91cc576298be3", "guid": "bfdfe7dc352907fc980b868725387e988fb83cd39e76a3225ba857b5b4ce9bda"}], "guid": "bfdfe7dc352907fc980b868725387e98780d7ce561e1e5e4dd84ba3372b8070a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98be05d80509cfddee5ed74221b06ffdb2", "name": "isar_flutter_libs-isar_flutter_libs_apple_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9872671e7f302554a0468d5bdae5bc6e44", "name": "isar_flutter_libs_apple_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 0}], "type": "standard"}