{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd836944b895ec74e93a897c6321285c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98373036e80fed0c7b7d1598b5935cd577", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd836944b895ec74e93a897c6321285c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a02b231a97b66ea9273ce9a0ca35c99f", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd836944b895ec74e93a897c6321285c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98642a46ad69d4558e2a059afc49e9476e", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd836944b895ec74e93a897c6321285c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e46e661cbce51dcbc7586e8ab5ed8765", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ce9cca661f0ac8f36a931513d8f6251", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982f6c978387bce46f7815a2fa01b8f43c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ce9cca661f0ac8f36a931513d8f6251", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fb1baadf0eb381506fae54f271407059", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ce9cca661f0ac8f36a931513d8f6251", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981074435aa3d81e366f426efab95dd0dc", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ce9cca661f0ac8f36a931513d8f6251", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984d48b6a31dd446d3d8a68ea4c988c44e", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ce9cca661f0ac8f36a931513d8f6251", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9899ecdd65d4575aeb3304791672354155", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ce9cca661f0ac8f36a931513d8f6251", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b47adb4c3df2aa3cd80d0f34d28a2840", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ce9cca661f0ac8f36a931513d8f6251", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9874dff4a4b3c17b16d0db7e5387175916", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ce9cca661f0ac8f36a931513d8f6251", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c5d6116d4815cccdbeb2c08a9457e3c8", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982896ea03daacf1a865965646fd070197", "guid": "bfdfe7dc352907fc980b868725387e981196b9930fe10e65dac6af492732e48d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c69a1d7ebac914fdcddeec62b6ed911", "guid": "bfdfe7dc352907fc980b868725387e98ce27a8fe13b7d2c580d989927da41d7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b16b13993c9a5453423ec37e2e06fc9", "guid": "bfdfe7dc352907fc980b868725387e988424ee8aedce1b4e157ec1fed1a0f316"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847cf8f66cf72083c6c999bcc05f39fd4", "guid": "bfdfe7dc352907fc980b868725387e98ac61936c806c45f7c3bfdd3195f5b022"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e47480835cbcadba3d2ca3845c108af2", "guid": "bfdfe7dc352907fc980b868725387e987426cbb5a0c5a02d1819965f01b0ebf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae911e97bdaea617771147cfe5e9f1cb", "guid": "bfdfe7dc352907fc980b868725387e982d0544e917acbaed1df3de5fbf7c4f96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeadb92e8eebcf03561b9b419dfeb8fe", "guid": "bfdfe7dc352907fc980b868725387e983b89108c9ca5bf30c09f35b64d3d41e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bbee5f0ea3690306486df8748fb6de8", "guid": "bfdfe7dc352907fc980b868725387e980aaac1b75e0620a4685360e219e8721a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6659ffce536134c1a208da52a81f647", "guid": "bfdfe7dc352907fc980b868725387e98e8490554d28d346f0aa7a9c1639bf9ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd6a8f915228faac94e38c60c9774ce", "guid": "bfdfe7dc352907fc980b868725387e984c62573ebacb59a2d2663b58bc63708f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98604a5044796822331c6df1c48e69b2db", "guid": "bfdfe7dc352907fc980b868725387e98091d70bf6991387f984cf273b25d920c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981adf2f71b15c4baf87a1331a1cdc7db0", "guid": "bfdfe7dc352907fc980b868725387e98e6a94931cdff11aedb26a9572f0ae943"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98685401040f5f9d735f64f538b6a8df21", "guid": "bfdfe7dc352907fc980b868725387e983e7e2bc6420ae22905188b420a9add0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c61084bc1abeaad4dde8304ac6fabb4", "guid": "bfdfe7dc352907fc980b868725387e982295179536bc4c5062799852f57138f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae3d8663eb5fcaaab8eba3cbcd10b3b4", "guid": "bfdfe7dc352907fc980b868725387e98c53ee657ec4756ced31f6b9252abb003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beeb0fc3fe2212b0f7790aedc5ffb3db", "guid": "bfdfe7dc352907fc980b868725387e987326cc654e6d163aec1208e79b526372"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983069cee1fec3fba7cda6630f32fba194", "guid": "bfdfe7dc352907fc980b868725387e981cd6f7c4ceab8b29077ccd2e9720f419", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8d20bfe59c8445d567dab0eb167ebe4", "guid": "bfdfe7dc352907fc980b868725387e984b7366fcabb379f82f9c6c5dce32d89e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847b3da0a166c287b2048c7737334b210", "guid": "bfdfe7dc352907fc980b868725387e98809dff299a1af536a6091626443f92e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168a0571d22e2513a36324400c08b863", "guid": "bfdfe7dc352907fc980b868725387e985de29a79c0cdcb0aec86559b0e949f5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98703a492c74e4c67d2dfe1fba1966e8ba", "guid": "bfdfe7dc352907fc980b868725387e989c0dcf980bdcfed1daec558093e2e5b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2f3e1ac0f91c249e61f12d36bb8926c", "guid": "bfdfe7dc352907fc980b868725387e98d84c8396bc78e20c947d03a4dcc77f7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98953b1c63f7f6312d6aef6779c34f2e7b", "guid": "bfdfe7dc352907fc980b868725387e98e2f72a0a92344ed9d3564c44b4e22944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fefdb18d72785c698df6b8ee159c1b6d", "guid": "bfdfe7dc352907fc980b868725387e980372b07fc4ee619faa01cf23d7c201a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c59dcbabc21e78e60588dfcd9099ade0", "guid": "bfdfe7dc352907fc980b868725387e981f3797a918c20aa7d77c9348b6ee1fc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5ef09f2e4afcd067e69401d67f86b65", "guid": "bfdfe7dc352907fc980b868725387e989a8001ac346025b597e34c12c2fe885a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7a4fae355c6be79e08426558473e698", "guid": "bfdfe7dc352907fc980b868725387e98eaab6783af354c2c00fb46234daff247"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b529015c1b83e85f10f70e686048bd7", "guid": "bfdfe7dc352907fc980b868725387e98a13dff74d90a74be3265a220528b7316"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcc54361ec61c84e88afed922e523a75", "guid": "bfdfe7dc352907fc980b868725387e9882f9635a6ff7b3b778fac71453d6eca8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986be9d687f1be2841f900604c3ec9e4e2", "guid": "bfdfe7dc352907fc980b868725387e98019174a8503896d647bb6a64d3f64373"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989530ec14b28467b02778dee503ef5230", "guid": "bfdfe7dc352907fc980b868725387e98fb251f01fed6121b3c36676affeb42f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822a7ed38ec2727047f2a75910ce5bae3", "guid": "bfdfe7dc352907fc980b868725387e98edaf6c13171278b36ddc24764af7e081", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f4d5fec3e4a291b1f81f7f05f83cd08", "guid": "bfdfe7dc352907fc980b868725387e98499907533d7a356d15e2a3a5aabf59e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2084e88b343f818d17ceeca8563b26a", "guid": "bfdfe7dc352907fc980b868725387e98a6001ac0cf4aa94828d987495c5e1c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe76cd05332a27fc3faf37cf15cfda59", "guid": "bfdfe7dc352907fc980b868725387e98a030f2d95cac9c5e5ec85dd349ae6495"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e01d27deb5456e6bd9447bc0246cad", "guid": "bfdfe7dc352907fc980b868725387e9870efb6ca9d98495021f90e915b5b1cb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a96bbe3104bf66a1041c6d056e761cd", "guid": "bfdfe7dc352907fc980b868725387e9845bf03653e30c6ca3e6fe55e46fc6dda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc753986ee3ff3cebf130e89b69fd663", "guid": "bfdfe7dc352907fc980b868725387e98aaa45029b89b3dcef67a98c4c2011d73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984530ec1d7b51ec66881261502d51820b", "guid": "bfdfe7dc352907fc980b868725387e985bd2fc26ef700e3570f04751dc648809"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e28fa30e85a4ae6881c42487298780e", "guid": "bfdfe7dc352907fc980b868725387e98096ee353459200db1a27e761ffbf37d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ed3e73640ef10f489b3a726ef639173", "guid": "bfdfe7dc352907fc980b868725387e98967bed531c937e94e26de3be92062e70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98792c389ec3d0c8abb782f3fe948d27c4", "guid": "bfdfe7dc352907fc980b868725387e98d23e4bf4b5b7906bf94ac83022e211de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805885d16dee5d068fcbf73899b0279d6", "guid": "bfdfe7dc352907fc980b868725387e982e2aeab1e181d04484f51dc2ad88ad16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883141c70310e0bd6be81c3608477486c", "guid": "bfdfe7dc352907fc980b868725387e98cf722e3c1a31a3ad6fe102f96770fc13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0a99f22fcbeba74d932707abce75646", "guid": "bfdfe7dc352907fc980b868725387e98079970ad2c420c4e4b28a3134f92d9ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf10861b7bc402050956fa82e9c477ff", "guid": "bfdfe7dc352907fc980b868725387e9884f3b99d50f91c99683e0f1803068dc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823ed84d1507a725ad31464193a206c2e", "guid": "bfdfe7dc352907fc980b868725387e982bcbe31884bf19e045fefaccdf6c1e55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e2d00ea834821a660cb56a55b342d8", "guid": "bfdfe7dc352907fc980b868725387e98d0b396448d40a4f5111eb6f1c2875da5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982be09a803e55bd022a324a3d6a7ebeb6", "guid": "bfdfe7dc352907fc980b868725387e98a186c03bb1f7622ae95f65e9864159b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a309bbd80f1d33568e9c2f1ef1bc183", "guid": "bfdfe7dc352907fc980b868725387e989bfe8827073752e1115485ae1df0bae1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98032e20f568d98f4bc6a89d1ca2c30d07", "guid": "bfdfe7dc352907fc980b868725387e98a99335fd3253699436a2ef61429e3d59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3d04d74a95d009493d172e7fd686877", "guid": "bfdfe7dc352907fc980b868725387e9822146d4ec0222847d20257b823663c25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981300f8f0fd0c22f78309b0a737008fdf", "guid": "bfdfe7dc352907fc980b868725387e9814063fd49664d6160a753dcd0b5c9101"}], "guid": "bfdfe7dc352907fc980b868725387e983cd901f8cd367fee5316adf2eeb24eb3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9879b2246495e5b977b29d08cc7734cda0", "guid": "bfdfe7dc352907fc980b868725387e98e01bcf6c25f154e4f06ca7b8d5375de0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bc2fe57a3e4a2b65b6ed3eeb85f170a", "guid": "bfdfe7dc352907fc980b868725387e9838cda3064856a1d2aa1d7944214eb54c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890fee3083074742bda394cac8144cd93", "guid": "bfdfe7dc352907fc980b868725387e98e971c076a97e35b543a9dc318ba0c232"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb435f84bd7ce662bbdc3858c6b4d0f4", "guid": "bfdfe7dc352907fc980b868725387e98e29c44002e72d8fa0d9de93ee670aef4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5a5877174e11b97d63794117a5a8435", "guid": "bfdfe7dc352907fc980b868725387e982099695806a649096f4eefbc3627a2fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ece1b219d8f89efc0bab4833dafd8ad2", "guid": "bfdfe7dc352907fc980b868725387e98c13417e7705c0312164c7747327d33b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987439bfad9692d219e3c19e7ea93d2c7e", "guid": "bfdfe7dc352907fc980b868725387e9877052051efe5d66b0f7fb580a77bca9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892fa64cc7a31c4f33d595b28db401896", "guid": "bfdfe7dc352907fc980b868725387e98064e5a9ee35cb9490c47e284ae506d1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca6f11313fb2be0c2937875441be4489", "guid": "bfdfe7dc352907fc980b868725387e98716d79ec587238e4edff37a89b60f821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eac17afe87574cc2ac5f273d9475ddb", "guid": "bfdfe7dc352907fc980b868725387e98eee2a899dee45781833fb5c4adee74e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888843ee66cc96db7c16d086f80b89941", "guid": "bfdfe7dc352907fc980b868725387e98798a6bc682bf85173ea7997da52de899"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bdeb7045d08a3e3b80fe35303a2c92c", "guid": "bfdfe7dc352907fc980b868725387e98990655a51e3a1a6735f7eae45c706ebc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6a8a33bc68bd34c9a81f7c8cede0bbc", "guid": "bfdfe7dc352907fc980b868725387e983b71ced5df42cfea21c67bec52b4a774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a15972b755d9591a30981a360a232a08", "guid": "bfdfe7dc352907fc980b868725387e984dff0d15c462febeee35f09931f22492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98159f09deb4396bc26e6a55922a4bf18b", "guid": "bfdfe7dc352907fc980b868725387e98d75c9c7b68adca45ce32d14d8dea54bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc0097205573ea5a26ce3710141cafbd", "guid": "bfdfe7dc352907fc980b868725387e9865e19b0dd43bfacd8860c4dc1d5412f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b85973987db7609d8991ac96ea272227", "guid": "bfdfe7dc352907fc980b868725387e98067b7ea4fa1400758d09b01cc4245341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f7d4018f2d9c3b61dae4039de28fd26", "guid": "bfdfe7dc352907fc980b868725387e98468686d2fd2f08a1ffeca0b313186c7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981489b86a12c9bfd8f3de5dbd77a5a454", "guid": "bfdfe7dc352907fc980b868725387e98172dbfb8553538f7cb371ab96aee730e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0654830555f6ab08fc36679ef727288", "guid": "bfdfe7dc352907fc980b868725387e98eb6a5efc5196cfb7a0cf9de15e482b1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c217386ae93dd024d479c771a7dd587", "guid": "bfdfe7dc352907fc980b868725387e98ec7cc7db106f3be7c134734048e5aba6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c2d91d71be1e44e6a38830e9ab93fdf", "guid": "bfdfe7dc352907fc980b868725387e980a92748210f3a3ef057133314d78ed92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c09bbf81bfda5cd55fe771f7d2558632", "guid": "bfdfe7dc352907fc980b868725387e98d798850a0468bdccebd98240c26509d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7d2575ca6d2eb71ae5f19b74b71dded", "guid": "bfdfe7dc352907fc980b868725387e98c74f95c3bd44f3cf9ac12bad96ddc78e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c6ffb7104d2dd5657429718fa082a0c", "guid": "bfdfe7dc352907fc980b868725387e9876ac46bf01b9770662c94b90478349a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887b82bf4600830b8f7dd53c5c651c4f0", "guid": "bfdfe7dc352907fc980b868725387e9899a2973e48045117264b3efee7de5a17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a7ff532b7363406f6ad70b2220f9a3", "guid": "bfdfe7dc352907fc980b868725387e98e7fcfec3c9caf85ed3232b023b9b5e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831322f40a5a245bbe606f171abcd4f7e", "guid": "bfdfe7dc352907fc980b868725387e98652f1e2faaa56cdf1663fb492021cd24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ed7a9b1a58cb5f36519f239fe7f9b6", "guid": "bfdfe7dc352907fc980b868725387e988df4f1220f6fa46d5a8851dc587d431f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98467361861851cca5a8ca572411623456", "guid": "bfdfe7dc352907fc980b868725387e98c997f0e4da09621a7ddc2514d42db341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869a5d35c9c6be883a6359cfafdc326f5", "guid": "bfdfe7dc352907fc980b868725387e989c6f15d171ead67151f92a8d6f4719d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988269ee8faa6b9af4bad007a7b2bb70f9", "guid": "bfdfe7dc352907fc980b868725387e9895ec5287a8b57dce8742819571a19290"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7e8934607175610f746d6277575fd8d", "guid": "bfdfe7dc352907fc980b868725387e9839af474cce7f2a0e03ebd9d5b9902e52"}], "guid": "bfdfe7dc352907fc980b868725387e985758ed7707494860a74e4302303769a1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e27d661740f9dabd2aa31605d055f69", "guid": "bfdfe7dc352907fc980b868725387e98a215c23f62c99f1ed9d6a14efdb17661"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4ddcb67d140e9c2dc1186b4294aac8e", "guid": "bfdfe7dc352907fc980b868725387e982b79d2a5ce24fc8ec72f10257c2ef46c"}], "guid": "bfdfe7dc352907fc980b868725387e98f4b7747e2566fc48bcdd9ae1c18381bd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98657a1426bd4839585c0755478c84a41d", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e9877cfab688f99a5eeadf954f495f0eb00", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 1}], "type": "standard"}