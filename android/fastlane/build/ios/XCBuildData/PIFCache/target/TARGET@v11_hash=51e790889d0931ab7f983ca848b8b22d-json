{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef59ffb2f35e7ff13933665fcbc0905f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986108222d6044db619b21a65a503558c4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef59ffb2f35e7ff13933665fcbc0905f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c2a1b7fff16bcbf5fbc801fe959033b", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef59ffb2f35e7ff13933665fcbc0905f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e50ea2abef2df3e0be5f973be6dacd7b", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ef59ffb2f35e7ff13933665fcbc0905f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cd81c9d774ce530f2a2c17db67afe131", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f01606d327425d35e4a1aee47832e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed17083f94904ab9ea7f38c769808582", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f01606d327425d35e4a1aee47832e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98156748af0d08e3d943ecde40d8872df2", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f01606d327425d35e4a1aee47832e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a915ebfc002d807f19604bb60d6cc39", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f01606d327425d35e4a1aee47832e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9838932fc0aaf7f72044d83c80a3ad8ec6", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f01606d327425d35e4a1aee47832e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989614fe03e7d09382fdd8e4fdc8eab4d5", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f01606d327425d35e4a1aee47832e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a228f0fe8085ce042dbb6735400a7f1f", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f01606d327425d35e4a1aee47832e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98640e99b6bbc524a23c9daccfc2d1e74e", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985f01606d327425d35e4a1aee47832e1a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b08110409b28b219a35f9b60b3d536a5", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980b6bf0029e1f450f35db5274a47448c1", "guid": "bfdfe7dc352907fc980b868725387e983d5afecc297572fb4e8d97f6565fec78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a5d44c664468579ae75c35b1e39b207", "guid": "bfdfe7dc352907fc980b868725387e986411d00e4722f0c22bb2b086b647d0f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835330118203417b88dca3828e32d9510", "guid": "bfdfe7dc352907fc980b868725387e982af788ff700a6d899bc8a6be4a706a5c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880d86a5accd40ac86da4b2aa148bef3a", "guid": "bfdfe7dc352907fc980b868725387e98349c497e062653218a3ac1fa67a65c0e", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807443d061e8a9ab1003f72eed67c250b", "guid": "bfdfe7dc352907fc980b868725387e9877c3950010f8e509b27e196924afe64f", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cacc0de7172ffabed962c8ded85f445", "guid": "bfdfe7dc352907fc980b868725387e9859fa5cb2130969103a10b39515534c3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b6df502f05a03c1f8fef6c2d7bdb7c", "guid": "bfdfe7dc352907fc980b868725387e9846984f68763564bd3c3ce2baaf01220d", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd02c83bb3f8d0d778df816a495595c", "guid": "bfdfe7dc352907fc980b868725387e981667da6c346e9338cb13af76eb20b9db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d72bec7f2d043176c715f025aa93249", "guid": "bfdfe7dc352907fc980b868725387e98e9f2f2ff426dfaa2c097a7f9da642c30", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983720cc66e860093684d07625a382bd98", "guid": "bfdfe7dc352907fc980b868725387e981b7b2ca1e2f83f72cc736e01779bbae3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98893d7ef809d771c5533b01d80793c587", "guid": "bfdfe7dc352907fc980b868725387e98abc43f8078dd5e52e966e35d815d8fa8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988652f306bdfbc0dea98e45da9f7f32bf", "guid": "bfdfe7dc352907fc980b868725387e983b0d326fa19f30cc5bad329b76fe9318", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e33f93b1405da05268c057387a6d856", "guid": "bfdfe7dc352907fc980b868725387e984065a4bbfef118fe767a9c0dca28c3b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861e82d3c2ad49319e76ad3e0d84333be", "guid": "bfdfe7dc352907fc980b868725387e98487a62dd0148683320d99bb73e099877", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826e4f72a13d425670b5ab03d2785e90a", "guid": "bfdfe7dc352907fc980b868725387e987821f0b0b43ee5c4cb0de17450f49746", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d92665c712d0470faca0317b0cd32e64", "guid": "bfdfe7dc352907fc980b868725387e98f756eaed7de355ec3250840dc82c9d3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2733532250f3df245a672faa88d269f", "guid": "bfdfe7dc352907fc980b868725387e98c4606a64e3a0a3f94078a6001c8da21f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5e722a3bffc33d0af6c7eb803612ebd", "guid": "bfdfe7dc352907fc980b868725387e98653ab7af0827375f11eb57cca30c1195", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a3de6875813b6d7f20ba44f129a25aa", "guid": "bfdfe7dc352907fc980b868725387e982ed5cc8114a74e83e9de24cfa3802c29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f107d68ea21013f8dd16831d24efd623", "guid": "bfdfe7dc352907fc980b868725387e988bbaea4c73095e7128540b7095bea59a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cb70d2fe5eafeacab294209edaddeb5a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9876568215874602c8bad26f5c4ba5fecd", "guid": "bfdfe7dc352907fc980b868725387e9888fed3e412cfb6659db27e86fce4a72c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9b622c67b4f9183da5b47bd765ee438", "guid": "bfdfe7dc352907fc980b868725387e98ca821a60837dd420d2997b5836506263"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982568e4da456b284f0f7808470f8343ca", "guid": "bfdfe7dc352907fc980b868725387e98f4b059697645a4cf475205598e0b1914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988be0a1956237ef283c05d60ef260c726", "guid": "bfdfe7dc352907fc980b868725387e98180778584173cab2b0f07446c1332688"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e384493bdf808e2806cd72d7593c5f", "guid": "bfdfe7dc352907fc980b868725387e987244c0bb6d764deaf90b5b344bbc7d05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe11fd5015768cbf97b8157caf86c2d3", "guid": "bfdfe7dc352907fc980b868725387e98db9345984961502998abb8cc5a4e29fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d25c1f23dd140ad5e42d3c30356d496d", "guid": "bfdfe7dc352907fc980b868725387e984e2de97870212251c41f69699b0963b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fae7da661e66e6ea08388caac576fbf8", "guid": "bfdfe7dc352907fc980b868725387e98e65249655877dfab8c7bca7d618a4663"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6d16e36c5aaf5aa95676f705f164c00", "guid": "bfdfe7dc352907fc980b868725387e989f5dd6d319b5ecc47f2a7b1abe8ac9fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878edde1a1ab1083f2410fcd31d853258", "guid": "bfdfe7dc352907fc980b868725387e988bfa16830820da2a14149847ec1c9dc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826f727c6107c8586f459952583d1c204", "guid": "bfdfe7dc352907fc980b868725387e984218a1dae79f99d8626108469f50ba50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98601e5403134384d63f97315971be9cd1", "guid": "bfdfe7dc352907fc980b868725387e980358d0b6db8e461fdb01e5da7a995b34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c6a9ffe6181248ea36b95fb211bc32e", "guid": "bfdfe7dc352907fc980b868725387e989a1e2e1ff2d029b72364bd8ace9c3e04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98252956593a1c5e50ded2c3395efc8d87", "guid": "bfdfe7dc352907fc980b868725387e98e3da7b349bd7b26d3f68dbff09957f2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ec74f9e03b94b86ded21996b1bd2f4f", "guid": "bfdfe7dc352907fc980b868725387e9857596fde3077f80dfd377dd2f5989218"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853a3d630e27c0f765b26b2448b09fd73", "guid": "bfdfe7dc352907fc980b868725387e9888c526dd4a70785404fa803389693a13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984739d678bc7ac5083f284462af7a59e3", "guid": "bfdfe7dc352907fc980b868725387e988cb0f9cfd235869ef6cdd8283eec1c40"}], "guid": "bfdfe7dc352907fc980b868725387e98421c9b9dc23747c3d4640512fefbe8ad", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e27d661740f9dabd2aa31605d055f69", "guid": "bfdfe7dc352907fc980b868725387e989074e530eb0ca8535886cd01bd872b8a"}], "guid": "bfdfe7dc352907fc980b868725387e988b6300266b6ce361659198e959e71383", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9822e68ec1de2d82a67c363a461ba3b2fc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986716a62ee19f61fc991dd3f4aa3e1163", "name": "Mantle.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 1}], "type": "standard"}