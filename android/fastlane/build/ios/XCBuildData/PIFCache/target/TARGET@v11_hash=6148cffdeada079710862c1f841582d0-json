{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b7e4cf3480a91f434da593f4e7716cc8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9884bc114cfeea70c1099bcaeed672e009", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b7e4cf3480a91f434da593f4e7716cc8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985c8dcbe39f42a09e4a23655a5c833384", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b7e4cf3480a91f434da593f4e7716cc8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c73b44cfcba73f338acb3b00273131e", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b7e4cf3480a91f434da593f4e7716cc8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fb580447ccd8b553083501c61ffa6451", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9883dd724a2e7f645181f73966ca4ba034", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0af2d0631f6dce66985f553869f52e5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9883dd724a2e7f645181f73966ca4ba034", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dd0d1bfb4cc2f4d8d9711f25c4f53f1c", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9883dd724a2e7f645181f73966ca4ba034", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cd4803dee2e96add21bf07df78d741f", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9883dd724a2e7f645181f73966ca4ba034", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9890d1ab54da93284b3ceaeaf65f7d580f", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9883dd724a2e7f645181f73966ca4ba034", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c3e613c29b077f7200261ef7e283527f", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9883dd724a2e7f645181f73966ca4ba034", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f9fceee2efeffaffabebf786ae6ac993", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9883dd724a2e7f645181f73966ca4ba034", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9895a4a1e6755362a1e45316065c3dc42d", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9883dd724a2e7f645181f73966ca4ba034", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImage/SDWebImage-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImage/SDWebImage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImage/SDWebImage.modulemap", "PRODUCT_MODULE_NAME": "SDWebImage", "PRODUCT_NAME": "SDWebImage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f605ef0e4bc241aa324de01bbd697d5f", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9864ebe1a9ea42cde675ac162e4314e119", "guid": "bfdfe7dc352907fc980b868725387e98a0831ffa65048befb5dffd6801327142", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5a9136f370a06a08e2db2c5a349de59", "guid": "bfdfe7dc352907fc980b868725387e9876fc1e0fe1a2cafda80d5c7c305f409f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fa3b4dc5902db3419ce2ab00f81cc90", "guid": "bfdfe7dc352907fc980b868725387e98bd8b7c775f842e1e7e85d9559fae530c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bb4c407c4cf31981846012679808cc9", "guid": "bfdfe7dc352907fc980b868725387e9849282e162083d664269bffe51194bd3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e6b70e53eef7b8453b2fe886f6e757c", "guid": "bfdfe7dc352907fc980b868725387e98359d76c6f256ed3ea72dcc3178507d56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efda5cb8e659d4cb3b881e8c9846da0e", "guid": "bfdfe7dc352907fc980b868725387e9829cd0ef2a85db02a1504d941b06d2fc7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986974375700b47d8b53f25a15870661c7", "guid": "bfdfe7dc352907fc980b868725387e982ab47a0c1510c69cf3841257514cb7af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db3729f3ca33ba103091100b73573440", "guid": "bfdfe7dc352907fc980b868725387e982cd180f978e19961108a2a7c672df498", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889918e9942fdcf083aa14a26e0586311", "guid": "bfdfe7dc352907fc980b868725387e984d94aa6b4cccac3faf0dbbf9170da755", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a758036613b6c563b2bc0f7ed2a56499", "guid": "bfdfe7dc352907fc980b868725387e989ad34a05b297cecdcefacb383bce296b", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98938274fcb7482b626928c755f288ed23", "guid": "bfdfe7dc352907fc980b868725387e984b5b5397a40874f1893af8dea9c0c457", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d92330f9a35914d0c4ea6435f84f67d4", "guid": "bfdfe7dc352907fc980b868725387e98d340ae3188a68510fc135f484c6fa642", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98446cd63ed078eb1e98495a1cec668c05", "guid": "bfdfe7dc352907fc980b868725387e98cbe3123fdb7de0c10da56356c8542e6c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98925d343f49f3049a9a2608b1b7649d68", "guid": "bfdfe7dc352907fc980b868725387e98e35254b928bc61196e18a1c63b9fd318", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98257f7112699ec2f0b5987a50853fc758", "guid": "bfdfe7dc352907fc980b868725387e98157af18b64e480e39558e2a7192390ff", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc78f8704aee151ae7888a4ebaa34ec1", "guid": "bfdfe7dc352907fc980b868725387e9840a4822ba03c8b28df397a52ca6b3b31", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984966947308da656022a37b8ec66c280d", "guid": "bfdfe7dc352907fc980b868725387e98afe8b0a314bec6f6832d41b38e78f4a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896791b9ee7ac1e8487b957a593269afe", "guid": "bfdfe7dc352907fc980b868725387e985e78341e20695d2a970191a17a82fa66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f1f11398093f054b8923d4a575b931c", "guid": "bfdfe7dc352907fc980b868725387e9835553a7060e0747653806b3d9d2aeb7d", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826507493b77882aed3ce6c2f39824977", "guid": "bfdfe7dc352907fc980b868725387e987b63773ede0cc7f9a0a61aaabf0e1485", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ff2a05c71dab2507c0cec774cce2394", "guid": "bfdfe7dc352907fc980b868725387e980dc51fc34f805e6ad094edc51d45c086", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfd8c93c492bceb5194135f1134355f8", "guid": "bfdfe7dc352907fc980b868725387e986a2c0785b3cb5493d9863f6c947c9ef5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bad94c2b844fdcbd0b2c0b4a0f9e18f", "guid": "bfdfe7dc352907fc980b868725387e98950e99b93d711be65477be11a62bb242", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981db62a6e2aa09d7763d76940a2a87b42", "guid": "bfdfe7dc352907fc980b868725387e98cf94eb3a3464a829ea609afad36166c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb0eca2b3aa41c4c38afbd4b8b3641fb", "guid": "bfdfe7dc352907fc980b868725387e98e2feb1c523f75fd2e4e1aa0074689866", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895c4c0d4883209b52fb172c3e5ef49a1", "guid": "bfdfe7dc352907fc980b868725387e98a5f42b718eb283d2699ca15a5fa93e11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861f49b468de1ba1e99fbbd645365219f", "guid": "bfdfe7dc352907fc980b868725387e98e443ba8f821faee226c59a0e86c75548", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877f175e93594952ab0bc781f65d86f48", "guid": "bfdfe7dc352907fc980b868725387e9867d3a63d9b27a84f2d2a33a7a1cc4f6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a45f3e767a592de958a0947aec80b7d2", "guid": "bfdfe7dc352907fc980b868725387e98c320deaf306b8ba18ae88c0d54851c5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5277fb965999298f92f37c664e14fcd", "guid": "bfdfe7dc352907fc980b868725387e98b000b5322ac9168de37c88fcf973b7e6", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf803d1616c12ac06e1f35679ed501a0", "guid": "bfdfe7dc352907fc980b868725387e987f91c652ffe7ad97f8ab86500f90f2cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820e7e8cccbf6c53928943587938dc4e1", "guid": "bfdfe7dc352907fc980b868725387e98dad071c9db5e0bd5b81752354b556992", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b53b170a0070c44a81f2331d39835fc4", "guid": "bfdfe7dc352907fc980b868725387e98633714014c6a32fb898f01a729ef3789", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e49b74c0e2183cedf31a7e1d5c2c601c", "guid": "bfdfe7dc352907fc980b868725387e984a390685d66697975c7eabb125aba716", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c80c253af3f15a04e1f27281d1486c2", "guid": "bfdfe7dc352907fc980b868725387e98bfc3883d69c0e6723a9b7ea364bf04c7", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886ec40678b7ce71613426a5d71ac2fd0", "guid": "bfdfe7dc352907fc980b868725387e98660065146558463bec6967c8b2cd17b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e52e4d63f46b6b568473c8a4be6640f", "guid": "bfdfe7dc352907fc980b868725387e98bf746650ac48578120fa94fd364ce2c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf7f37d45ae99cd4788e2eaba4277195", "guid": "bfdfe7dc352907fc980b868725387e984ee1822101f9944797cb39523b76f85e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983df83962425e634a686ae6b8fb5cf23f", "guid": "bfdfe7dc352907fc980b868725387e98c5b79c21f02cdb6cd8c07a0159889409", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98321684265cf318002d8e99b7a0eee14e", "guid": "bfdfe7dc352907fc980b868725387e98f2a6efaba179aa9e79e5b218b29f0467", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf93c628511ec233232b1b98e11e2ba2", "guid": "bfdfe7dc352907fc980b868725387e98e0b8fbbd53aa515fbc365baa7ba91afc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ce29101e6d3feb0159d2290d8d40b88", "guid": "bfdfe7dc352907fc980b868725387e9879764d5ea109c747eefe3a62d219c38e", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ea0d355e43ccdcbdabf00425a79767c", "guid": "bfdfe7dc352907fc980b868725387e986a329f5767e64610590ff336469bf6dd", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b63cec4fabf76555f9b743c67116b4d1", "guid": "bfdfe7dc352907fc980b868725387e980868e4276012ec02e4d878b517dd462c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983412d4284a08628d57bdc2f989207897", "guid": "bfdfe7dc352907fc980b868725387e98db2a3a20962d8d8529058a053f9fe6fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aac3f38b30566cf69337505df70a5a98", "guid": "bfdfe7dc352907fc980b868725387e9862c68f8490116c39eac992eb1725506b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d8020349273e72fc95364c251a7e182", "guid": "bfdfe7dc352907fc980b868725387e98eb53ac38520b8eb0e4fc9f01f326b91d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f67288fbf634a2a0ebbc57ae6cd6ae89", "guid": "bfdfe7dc352907fc980b868725387e98a07dcc41fe6d61322a30f0e6d9ed9cbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd9d42e9993ca607dc0863b0c4d6e2f", "guid": "bfdfe7dc352907fc980b868725387e98c746ec2e1bfc11d7533028dd64b559b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864d76dc159a824c70cb6c16b26671590", "guid": "bfdfe7dc352907fc980b868725387e98ade237fbe4a6b5805d680c703e3edac2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d7b21f85d810638b42a370cfb975d4", "guid": "bfdfe7dc352907fc980b868725387e980cc2badb414729339aae637c3e2aa36d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989221e5e2e09f428d61bf19cf54f5306d", "guid": "bfdfe7dc352907fc980b868725387e981288567fbdca37f477cdefbeca456924", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f77c267027a1f3e183499555e2a35d13", "guid": "bfdfe7dc352907fc980b868725387e9837066df3899caaa3083eda694656e24f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d50897331e2e756e32a9405d5cc4d7b7", "guid": "bfdfe7dc352907fc980b868725387e985d34065f9b03649c4f50c04c9bdabd8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3d2000985b49df0590a4e628444ad3a", "guid": "bfdfe7dc352907fc980b868725387e980a3e4174ef2caaaf22e553bdce432fa7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98038ce1b2554c4921960f28516defdd56", "guid": "bfdfe7dc352907fc980b868725387e983067a1f607a1905af0c99563760c5649", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1112fbb835c2bec8035fbbdeab5dcc5", "guid": "bfdfe7dc352907fc980b868725387e9893b85f33df7fa3a8c1452f3078332f73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981020e27f91800e870fd7bf94df94ef3b", "guid": "bfdfe7dc352907fc980b868725387e98da0610db9ac4247d8aaae3320c593e3a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4bb7b422e25e6ea8977f449245da5f1", "guid": "bfdfe7dc352907fc980b868725387e98102084913fc2db9174d6a0ac0b533328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a797cfcb3ea92b48fc47b9c7ad063d9", "guid": "bfdfe7dc352907fc980b868725387e9819ef7eb8cbafc018769798dfb5373c75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832c5ecf12ab8c07da8d9bf579b0f4b2a", "guid": "bfdfe7dc352907fc980b868725387e98f075f8a00655dd2aceeaeed56cb46591", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849e2f6e76a2d9f5e9ce2070058bd1902", "guid": "bfdfe7dc352907fc980b868725387e98e72061d953b81f4750869d0be25b9078", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894ef8b2439bc01f7637c4eb1dadfb6c8", "guid": "bfdfe7dc352907fc980b868725387e9826f2247ced5d2b802fe8ae437e3076eb", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98553cc5e62aa70710179ab1fb845eaeaf", "guid": "bfdfe7dc352907fc980b868725387e98f0f1a78585699e77eab7dffb0183807f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981032f95721f7517dc4cd20f80ae745ec", "guid": "bfdfe7dc352907fc980b868725387e982af643a18e0093bc18196203f7aff3f7", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987504fc29df1ae2168a269744a4536bf7", "guid": "bfdfe7dc352907fc980b868725387e98eb7570e10e877d4eb3adbf932529c95e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fab5a2797d42256d846f0ba3bb86987", "guid": "bfdfe7dc352907fc980b868725387e98cd897d2a533f08971ac4f8b272357ed1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984183860be2a3fd5061306ce90363a128", "guid": "bfdfe7dc352907fc980b868725387e98ea61adc8178b8b7fcf6cac6719411a1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e2aac19e5e0b6fd9a8be1cb00979c05", "guid": "bfdfe7dc352907fc980b868725387e986c92d9cd5620ce8d57cf436a220da4a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988db0fab60611f81504c1b90ec36b43e5", "guid": "bfdfe7dc352907fc980b868725387e987dfd0602cc92ae7a9f56d22a6579bdf9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff1a13304373b2c10701381bfb0ac413", "guid": "bfdfe7dc352907fc980b868725387e986c8b13ec16804af9451a20acef59383b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983540db1657435b268a49e6c70e6c4fb4", "guid": "bfdfe7dc352907fc980b868725387e980451765273f45b75ac7c7c1d367d8dee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f409828ef109347587d19d768c61a93", "guid": "bfdfe7dc352907fc980b868725387e985d3ed71ea20a765d0127843f11513646", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc3a40f48a6e6b7db1795834d376239f", "guid": "bfdfe7dc352907fc980b868725387e984f34013febfece5c675a49e3e3b7d67e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e0a2582c9f9eaa563bcc415a1f52235", "guid": "bfdfe7dc352907fc980b868725387e9828edf175d685a8997d9172361d4b4b93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6f2dda7fe782ec56bce0f065f7c3360", "guid": "bfdfe7dc352907fc980b868725387e9848f6dddfaba91489dfb6cfbaaf5a6712", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983be1d125f2634a57c7279d7a0f1b9aef", "guid": "bfdfe7dc352907fc980b868725387e98f0da75a2a2afa036df31aca309900993", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98882dc0b78fe96d2d23b479551b9a6fab", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9871d7f092ffdaa6d3d8d8a9f4bb559ffd", "guid": "bfdfe7dc352907fc980b868725387e98a604b09330562348484de247d9a22e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a011884abdfb66d2a5ff05202f491456", "guid": "bfdfe7dc352907fc980b868725387e9809d0e1539b62f3144ade6c78b6a91956"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889e3d01763cf4f5410000e3c53351a44", "guid": "bfdfe7dc352907fc980b868725387e98443112ee8d2a673a2419e0c69590da2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801df05b48ff2ba4225a42b03b8271d4f", "guid": "bfdfe7dc352907fc980b868725387e98a60d8fbb155aa2f0b82c6f7c972ba1f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d8a3b7b90287ab37b5c28631ecd0543", "guid": "bfdfe7dc352907fc980b868725387e988458e5d978af037e1416a96219b84cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985617a01717853bc8a718e6c4886fe6c9", "guid": "bfdfe7dc352907fc980b868725387e982c647179bc88bf5919f8ebb9ca70275c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa1b01534b4e5a61caf6fa2eff79394", "guid": "bfdfe7dc352907fc980b868725387e98819af0d7c5fb4b5670751565935a8421"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d79d3b53f453062b019e422eff6c3ad", "guid": "bfdfe7dc352907fc980b868725387e9884098f0782f5076cb19474ba340b1bd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825fc97dda26254a37ee91bc288c24b36", "guid": "bfdfe7dc352907fc980b868725387e983205725ae12f8bf0f6261828f2d71442"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cb7dc110738afb31e0889a63653abbb", "guid": "bfdfe7dc352907fc980b868725387e98676e5eab9c1ca38c4e3f81aaeede48c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae33b99048dfaa07e4c4e131964c77d", "guid": "bfdfe7dc352907fc980b868725387e9897c9f41ac7b1f6895cd7cd33d7c03f09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d991755ed56cada8a7c2af5f3c86901f", "guid": "bfdfe7dc352907fc980b868725387e98c508847e9d6d4b0519f2085e3bdb484a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9d3190c2d5b283e719c53e0511b59b9", "guid": "bfdfe7dc352907fc980b868725387e9863813bdc998f8811aa61cde61ee1ecb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988be06ac7259673a252b037b6f8bd6d18", "guid": "bfdfe7dc352907fc980b868725387e98fcf3b2e0391af27162df662963b28767"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983df7dcb00cf78ae42741c5789de26e47", "guid": "bfdfe7dc352907fc980b868725387e98d95ba387b438ef8f40bde6c568a05fca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea6d455328e2b85524653102e12d97f4", "guid": "bfdfe7dc352907fc980b868725387e98cb174e24ddc0e56926557180a8cd15ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a93ed834f3778dffac5aabe28c99cbce", "guid": "bfdfe7dc352907fc980b868725387e9849d1aa97a2b1194346272bc008337a17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b123e253c365d1c7d2050b87b7a211ba", "guid": "bfdfe7dc352907fc980b868725387e9857963b4309f8c920240694fd2ee0c2ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98769c3bea0a4a30285b9c1e3765f100a1", "guid": "bfdfe7dc352907fc980b868725387e98c81adb56490c96d4ca73129602e4c613"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7e2bd9bbc91b37448f534de8f7b75fc", "guid": "bfdfe7dc352907fc980b868725387e987cce424c746551a50e8938c6c51459a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c56678cf8bbc5e854f937c0e832c7cf8", "guid": "bfdfe7dc352907fc980b868725387e987d6b145060a27d16c4e583479f2d3607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeb477371f84110beb5227a20af06b1d", "guid": "bfdfe7dc352907fc980b868725387e98b85fe1c2658819ecf2a8df3c4acb1e5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168f6e5d2813dc67adf4ef89a2be5386", "guid": "bfdfe7dc352907fc980b868725387e98757782f193ebe3388fe6e0800533a41d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ea9bd8bd1862d55cd99c2d9839a027d", "guid": "bfdfe7dc352907fc980b868725387e98b4590516539320b9896e6593eb639b96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d90746f1e143fc6cc00f597d098b6273", "guid": "bfdfe7dc352907fc980b868725387e982241d93f7fdfa6490b699fc7e1425888"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890948a9c2ea3eb2eb692337cf38eea64", "guid": "bfdfe7dc352907fc980b868725387e986601bf6a2ab114a5b2d65d0db22ceb6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c37eab26b9584676e03ccca3c732f4d", "guid": "bfdfe7dc352907fc980b868725387e98bade3c99a3a17880719899649ccf083c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf0eeb31bbc387a35c2a95d7169df534", "guid": "bfdfe7dc352907fc980b868725387e98837ef927e2a8b37af8b1ae0dcc72d4a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5c03368bce1853223167d5a8f964cab", "guid": "bfdfe7dc352907fc980b868725387e9881bd9a6b0bee5c2b68c319d2e1069371"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989668cad7c5acbf43ad6e184a7142a788", "guid": "bfdfe7dc352907fc980b868725387e9822b95d932349c1e42ebfd18751041cfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d436bddd533a71bc3a92f7da96a564b", "guid": "bfdfe7dc352907fc980b868725387e9827343c75f89e3b8025a63ee666428dcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f96e0d8ce84dcbcc3ef9ec11a95a6ce", "guid": "bfdfe7dc352907fc980b868725387e98efe3c85bf2b095aa63b0c8e323eba4d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b775dd75ee2e49cdf11f4e83c10559c3", "guid": "bfdfe7dc352907fc980b868725387e9837e9f35b7c1f9048ee73459e856ba179"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d4c99c668ef644688aa88b606b3dfed", "guid": "bfdfe7dc352907fc980b868725387e98e027536b11837cb2569de822271368e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9df258338d30dda44cfcf0c8d7b75f3", "guid": "bfdfe7dc352907fc980b868725387e98c1cb9c31964da366538f355a07a4bcdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981152ccb6f68f07efae8048be0fe01577", "guid": "bfdfe7dc352907fc980b868725387e9806f6ecc7538a7e664fbcabe8969ed36b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f26f0c15fb448a698843855ff102856", "guid": "bfdfe7dc352907fc980b868725387e987fbb72e830cfc5250b3cec8a81937f1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98859284064bf1da57dd62fabac461d111", "guid": "bfdfe7dc352907fc980b868725387e98b33111c55b928ff34396debebe4e7355"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802d78232b8e462792d9dd4ad2d19f460", "guid": "bfdfe7dc352907fc980b868725387e98de13d6ed5cb996d77e230c010229b5ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98214440c64d13819641107e9e11827149", "guid": "bfdfe7dc352907fc980b868725387e98837142f01ef779935bbf91052f04dc85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e76d1fdd608cb61a1e1568a4a0579ee1", "guid": "bfdfe7dc352907fc980b868725387e98b4090f6bbfe60167fe325a5dda66bcaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882ceb060f9571915a1a691043c6787a8", "guid": "bfdfe7dc352907fc980b868725387e982bd4afb801e4d31ee4f81d038b3c8ed6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98490f8df6e672922ac95949402eb4c0b2", "guid": "bfdfe7dc352907fc980b868725387e989973b8d9e6a3d58957434e12cc12a3ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5cd66219f9281d410c9a5d720f70ccd", "guid": "bfdfe7dc352907fc980b868725387e989d8007bd97180cbe96bdee5ed5e046c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d45539a92f86cc5206e7d7a2c95640db", "guid": "bfdfe7dc352907fc980b868725387e9807cd5fdc8ec95cc3e7cb4c635aac66ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a04579b1a3252d98cdb5a91a2aff0260", "guid": "bfdfe7dc352907fc980b868725387e98ec294b0850b03aa3126ba974254866e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f83f14a8da6a7ca5545bd9ee0c48b5aa", "guid": "bfdfe7dc352907fc980b868725387e988930236a9e83acefb88c65dcd1448a8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984df7e56a1c577d6a5e3d4e8b0622cac6", "guid": "bfdfe7dc352907fc980b868725387e987d653332750988eed8eccbad1438612f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a45dbafaaebc93249fd25316869dc43", "guid": "bfdfe7dc352907fc980b868725387e9826b5f4e237d8a07a0899bd867b61a9d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e6591e048edc8d10573be71137bea19", "guid": "bfdfe7dc352907fc980b868725387e987ecb8ebbde0781dd31f837d7224c55da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98615e1d3362422033bbd4bfeba31a3426", "guid": "bfdfe7dc352907fc980b868725387e982ef182e9b27398b564c541924aa1ae0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1bd81220493a7ab068e409593012fb9", "guid": "bfdfe7dc352907fc980b868725387e9824de1534ec53ad70d8adebe12d77ebeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c4df69a75380205fb165ecba0e1f531", "guid": "bfdfe7dc352907fc980b868725387e989e8c2f5d05816fd0e387821bb12c8876"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c2ae2a93524d6ff05005f659364c411", "guid": "bfdfe7dc352907fc980b868725387e98ede60f89f8064b0a833ef7c62e3d5c78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98344a689965b79a0c0aa8048a099d8c4d", "guid": "bfdfe7dc352907fc980b868725387e9864faf7655e980b919fe3986579c924b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98822551a20ba094bd9cff0da1e7e231a4", "guid": "bfdfe7dc352907fc980b868725387e98cf000abdf8d2ba1b6f834139a0115c5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad700658d6c1bcaab965c8b43a047a8a", "guid": "bfdfe7dc352907fc980b868725387e9870dddbe20e7af454ae9802069697f214"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98076bcd61c103bad5477bdafe1910e8e4", "guid": "bfdfe7dc352907fc980b868725387e98e736f01840ae399465f04dcb31f7eb77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15bc73f65122dada8842db18fe2e874", "guid": "bfdfe7dc352907fc980b868725387e986be6ff0397394290f87ef2d3bcc878db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845aa0747cc6a1249b46a85da6c0bdacc", "guid": "bfdfe7dc352907fc980b868725387e98b52232319b5eaf8836bc5b988b91b9c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a40ae9278a61ecac7971897437eb0aa6", "guid": "bfdfe7dc352907fc980b868725387e989327b7b5fab800f5bb33c2f067b374e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b17fa4a79223df05dff76348a0262a3e", "guid": "bfdfe7dc352907fc980b868725387e983be8bb0a5b25eaac7d80492bf2aaf19a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98637014ce4ea08cc122dcf1010623a4f9", "guid": "bfdfe7dc352907fc980b868725387e9833d0e7d57888dbbea201ca4fcc083634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a00ff45a8e10fe233dddbb20932441a4", "guid": "bfdfe7dc352907fc980b868725387e9816be4d0fc68ed5c98e2d0f931c2eefea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2aaaa7eabf400e137dee7ad9344bee2", "guid": "bfdfe7dc352907fc980b868725387e98f7a9d5e0838dd6a7122375a8ae0d4063"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98246317e1a7095f7e5c851fc2694fdbec", "guid": "bfdfe7dc352907fc980b868725387e984938896343704ed8a2c3d605f18d3bbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98071660f38ad4140c175ae039d7cc885b", "guid": "bfdfe7dc352907fc980b868725387e9854299692a1141ee89ca284a89dd96f7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff23cb577359da2097ef4a1e13123e46", "guid": "bfdfe7dc352907fc980b868725387e98fd55d67d667f92d7b2d2fa06ea77f21c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819512e8ef5105129773c2370b0687839", "guid": "bfdfe7dc352907fc980b868725387e9877abeffe637c3558fc739ca3a89a8d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878a75bd3f63c757b5054b2b203f3890c", "guid": "bfdfe7dc352907fc980b868725387e9875efaed47b22726cf7fb4de8da3edf5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e469c5b22d180f8fe556151696de463e", "guid": "bfdfe7dc352907fc980b868725387e98d5e5925bdae3b0e107b35fc2df9ae9bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f2090e195f717eb0a4457183ae4e2d6", "guid": "bfdfe7dc352907fc980b868725387e981adc38ef5f4c767de522bd520077259d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec13a4caa072dec20d334f73683ccaeb", "guid": "bfdfe7dc352907fc980b868725387e982b9a920b4830457879d4b2614ec82e77"}], "guid": "bfdfe7dc352907fc980b868725387e989c6a1dd8c5d66b434e384e5678871841", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e27d661740f9dabd2aa31605d055f69", "guid": "bfdfe7dc352907fc980b868725387e989b84776fa0359270ab40627efea579c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836ad16a50b50572b5c497629816003e4", "guid": "bfdfe7dc352907fc980b868725387e98433028e9c1489215eccd8b6ca3f8dabc"}], "guid": "bfdfe7dc352907fc980b868725387e98087969af45db61929de8a24ad7935749", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a69c06bbc01fd5a061582d15ae96f34e", "targetReference": "bfdfe7dc352907fc980b868725387e9826e2628dc041aabe2d77e75ccb1dc95b"}], "guid": "bfdfe7dc352907fc980b868725387e983858336d56d5d8e3bb3be09c180786f4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9826e2628dc041aabe2d77e75ccb1dc95b", "name": "SDWebImage-SDWebImage"}], "guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98880bd43228ac3a23ccb630571006ecd9", "name": "SDWebImage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 1}], "type": "standard"}