{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2a9ae1d532f12869948f21c9fcc722a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9846932a473dd593b7b243e041c6832171", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2a9ae1d532f12869948f21c9fcc722a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a1ff9de0368665cdab07f094fc0ca78b", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2a9ae1d532f12869948f21c9fcc722a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9834b8a4dcac4a36986f75f44120df777d", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2a9ae1d532f12869948f21c9fcc722a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c75ab984a8019330c239e3d38568550d", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818c0f72c1644297aa5537a4551919406", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984553c6200d22b7711a1c513a0386966d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818c0f72c1644297aa5537a4551919406", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b105ae077852381110523d0b482b1720", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818c0f72c1644297aa5537a4551919406", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983cceb3214eff6107e6a2391d46ee4e68", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818c0f72c1644297aa5537a4551919406", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b6f76a55643c1581995818f51914a21a", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818c0f72c1644297aa5537a4551919406", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989c8dfe0c53e8960bc0008d72274fb714", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818c0f72c1644297aa5537a4551919406", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ca1d0c7357854dce289706a5d1bc43b7", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818c0f72c1644297aa5537a4551919406", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982863ebc1d52ed755a8a944335585557b", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818c0f72c1644297aa5537a4551919406", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9870df907d2f789b51e3d20cd56c43bc40", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b989a6577952d7ee018b7c4b4dd0bb83", "guid": "bfdfe7dc352907fc980b868725387e983b7fb7f9e2287da4fccd8fd4d483065f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e932a1c140ad6839ccfaa570c205a635", "guid": "bfdfe7dc352907fc980b868725387e98c0330db2527b9d0859d082064b12445a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852cb5e21e27326e7a4cd937250f09b97", "guid": "bfdfe7dc352907fc980b868725387e98986d78fbbfebc2173a3385b8a1b5057c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe0f8aba64e9c13f861f6b143fcf597", "guid": "bfdfe7dc352907fc980b868725387e98d1b175774f6a3e51a8d923d897801c8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981be536225da63a38a0dc50d77287467d", "guid": "bfdfe7dc352907fc980b868725387e9838327919773091e31151381fae076c38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a844e36e76b581aad4483f0c77e59d3", "guid": "bfdfe7dc352907fc980b868725387e9842d7a07d072fe33eb17251694e1f4eed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0fe166a6e39f7bd246c1630b121fd37", "guid": "bfdfe7dc352907fc980b868725387e98a3d63a1ccf1ea045394faf5a8b5668ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f05fc16b511b3a046c9f99958fd546ae", "guid": "bfdfe7dc352907fc980b868725387e98fd822606597f3bda02945534f25cd799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea6c9ad6a0e9cea44d4c93c89850c89f", "guid": "bfdfe7dc352907fc980b868725387e9825d880d203760fb92251dd0bf6a0e3dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801513988b3d285911beecea003178d32", "guid": "bfdfe7dc352907fc980b868725387e989808586bf244f3bfa92da993fd501d47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c3d814ffaaff658a2f8575ebf8c2b56", "guid": "bfdfe7dc352907fc980b868725387e98e246d9b8c7cb6288f59bce181c03d1a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842c6d3603be78aa37e75daaa48bd0f24", "guid": "bfdfe7dc352907fc980b868725387e98a48e2ea405ed6a5c4a1c24fc76c836d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f28ed2c4f9ac24ab8d1f117153e0731", "guid": "bfdfe7dc352907fc980b868725387e98219ba25d74eec5c081c3c7abe364d41e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826b0738964f0bee14e8eb00082f682fe", "guid": "bfdfe7dc352907fc980b868725387e9855d5628e8261077e263820e89ff1d3af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b0557302a47c4358bdfde06391f0f74", "guid": "bfdfe7dc352907fc980b868725387e983ad06f69430c8f85c23e7d8b73538ead"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831e490f565c797e1f5616d4f3be44af6", "guid": "bfdfe7dc352907fc980b868725387e98c4d4a1d97cbd466faf279d63698432cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98421d1a5624d82043ff8b23fee40e47e9", "guid": "bfdfe7dc352907fc980b868725387e98a147687eccec089b1569c336057ffc5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f2882141701e60b205ea22d32ccfae9", "guid": "bfdfe7dc352907fc980b868725387e98474551efb010ca833eef6cb4f246d9d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b933c77631ae28d643adc16013eb084", "guid": "bfdfe7dc352907fc980b868725387e984258ca07456de4ed8da972b2daac75f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985399aab65400bab67a9a71303821d18c", "guid": "bfdfe7dc352907fc980b868725387e98e56ce3e222e255a04df71a0c6d3e78f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e0376bb4d3b5b62453e83d6898c44ce", "guid": "bfdfe7dc352907fc980b868725387e98b5023b88699e17d28abbf0177bf13627"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eedf3c9cb76f4cde95ab03f1c0d8d64", "guid": "bfdfe7dc352907fc980b868725387e9854f050cb826ae2344f7f9d6e00fc8a2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df752c758b17fc80e1e7de677cbef589", "guid": "bfdfe7dc352907fc980b868725387e987339aa7f1a6bd3dfb2dd77fc5934d618", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3c20909158e5b0a6e4e25c1d8f8a979", "guid": "bfdfe7dc352907fc980b868725387e98248641ec6372cd6440a137e52db2bc3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bca4baad0bf117aef130145c06cfd99e", "guid": "bfdfe7dc352907fc980b868725387e98c27928f1e9ac8229004b362a9fdddcbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888ea28837b61667f1f049076d05c2416", "guid": "bfdfe7dc352907fc980b868725387e982f64d705268053d059a3807a836df5c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfe4fe137ff9fd16ffeb2333b18dc06e", "guid": "bfdfe7dc352907fc980b868725387e98357fafdcd1b14f7b9b25fc78a84af6fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897108113e2fe16bf6fff972dd7ac49a9", "guid": "bfdfe7dc352907fc980b868725387e981b5616b9ec77e2d7ebc31fb14544d081", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cea4df0e5a5f027f57ae1c341aca3ec", "guid": "bfdfe7dc352907fc980b868725387e98c51ed224225aaeb35133170fb2ddd768", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e10b6ba2d6645efbc784d5993096c11b", "guid": "bfdfe7dc352907fc980b868725387e989e221a5b5555e04d21539f22108234f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe3e67ec982bad0136e02af17e2991dd", "guid": "bfdfe7dc352907fc980b868725387e98dc10e4e8c7e70a07333f91e2b48d1508"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d35cad6694e133559a4de15b6affac9", "guid": "bfdfe7dc352907fc980b868725387e9824efc0f693c45a89b0a360fb89863b1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8abfcfcaafae0b536cb6355134d8c97", "guid": "bfdfe7dc352907fc980b868725387e980a24cc7c687e89ef91cefe750955a43b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2a7804f01d055dedacd962c1c0ba9d4", "guid": "bfdfe7dc352907fc980b868725387e987d73ec30f36b7219e55ad99edba7019e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2294a7d05da551a4b09b6320ea01500", "guid": "bfdfe7dc352907fc980b868725387e9874b12e591efaa66efa1371077fb5eb68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1b2de3593697410b582aa3692e4104d", "guid": "bfdfe7dc352907fc980b868725387e981e99067cdf1c58e23d17fe159205a751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d6d9631bee3f7c9d5230f2ae7be33cd", "guid": "bfdfe7dc352907fc980b868725387e98266b20c0496970f63a9c880596dbf497"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9ccb943f81926236122ea6832980165", "guid": "bfdfe7dc352907fc980b868725387e98010c4df6ba9804702b3888354f532b51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823f23046832901dd4d66b612bed68913", "guid": "bfdfe7dc352907fc980b868725387e988318b5954159f4ca6be6fc0eaf5bb873"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c8417f804320d6e030d51eaf209b3e2", "guid": "bfdfe7dc352907fc980b868725387e9834b4de9044b0c74c2d5e6c528e5d971b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98314b6b806a564165ed595f96438ecd81", "guid": "bfdfe7dc352907fc980b868725387e982063b8015d77707847c9cb3bcf55aee2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc3bb52d557528f4daab09d7e81c7c6a", "guid": "bfdfe7dc352907fc980b868725387e98803bb1712aef478f51c396758b5e9b48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897262d06272d3e948c0fe337e729239e", "guid": "bfdfe7dc352907fc980b868725387e981fcb52ce57c797453d3ca8b2c465c069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98929ef136ac415a3745b43c89730cee38", "guid": "bfdfe7dc352907fc980b868725387e98756169753c7e7881d8cfec77faa4176e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecf16d9c0ca1d712719ce4c8345cb2a5", "guid": "bfdfe7dc352907fc980b868725387e981a66c5bc3e86cad01f52d8c2c876b410"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbd3eb7c7cb28b5a3a0f81bd05619537", "guid": "bfdfe7dc352907fc980b868725387e98109d97d945ea8a0c7be2f673c75f6305"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865ed28f7a317765a28b11d51dbfddc84", "guid": "bfdfe7dc352907fc980b868725387e9806b8e241bfd7f82c2483ed1566b37751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98726bca8b9d6cc6335970be72ffae6ce3", "guid": "bfdfe7dc352907fc980b868725387e98479da27963572ffc2e5861b8cda51175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d4d74a3028c2df9b1b46faf96b3cb7", "guid": "bfdfe7dc352907fc980b868725387e980a480eb52ba6c4ce30ce0a1e2c9371c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc27f9ab5c5caf88165e63172f5af4d", "guid": "bfdfe7dc352907fc980b868725387e98edbcbb0555086c0cbe19bb21129780db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e2dae8d1d987e6c8bb42e2953075af1", "guid": "bfdfe7dc352907fc980b868725387e981639f9d153e6180235210e570cca5482"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2d59cd61921510e87c3a42ce4c07115", "guid": "bfdfe7dc352907fc980b868725387e98fd7009f1e9fa4ef9cf4741c917b912fc"}], "guid": "bfdfe7dc352907fc980b868725387e9865d6026566ae0e3b802950b60dc8541d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d02b498e460d3b9f59c65af85b9f54b4", "guid": "bfdfe7dc352907fc980b868725387e98b5c33c4535be7f3709468cbd04493e58"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d4cbbc4a346be84142c44917a090b205", "guid": "bfdfe7dc352907fc980b868725387e98379843078f86e9d29f62fff6cd839f68"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98dac18d5cd782fa8e44fa29fc2856c61e", "guid": "bfdfe7dc352907fc980b868725387e98a8556d1cb17a80033c0b2440d69ce679"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98eccb2c30674565fe99b829b5b5b86a6f", "guid": "bfdfe7dc352907fc980b868725387e982a0a283601f6d6721f569a035b645d34"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9872e76e702a83ed052df0738dfc5b534b", "guid": "bfdfe7dc352907fc980b868725387e984936c68648b962b848acdc267af04d22"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98dee36b49679b43a97f94f77511b1dc42", "guid": "bfdfe7dc352907fc980b868725387e980bf1c340c6bfcb4020d06233565effa5"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98812530d406d0813145eb4b0dbe0ca8a9", "guid": "bfdfe7dc352907fc980b868725387e9846975888786f31b229ca3db56c5c0868"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b61a1eeb95d7e377de107e2178ef9b81", "guid": "bfdfe7dc352907fc980b868725387e98c9a2985819520129f95ce55cd96add11"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9814b59e2370127f7c1d56f63a8b7a6905", "guid": "bfdfe7dc352907fc980b868725387e98ca7b76915af460a9b0d502376f3cdf8e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e984ae9bf3463752848e8b0885e38f5693f", "guid": "bfdfe7dc352907fc980b868725387e98eb75630598e73ddc8d82c8967aaf859e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98137efb257b0243756b22baa9b9448d47", "guid": "bfdfe7dc352907fc980b868725387e983fe1fb116d0c6d6f1849e55675901188"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c32c53b13ae9c553600a2831cffa95ba", "guid": "bfdfe7dc352907fc980b868725387e98a18bba1541b33693a4677c8461e3a47a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c37c1796aab0f7f3063e1e923783366b", "guid": "bfdfe7dc352907fc980b868725387e989c6bbc81556ad4509db2e86a342d0525"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982937b0feeaaf07293d84b1660c2ed456", "guid": "bfdfe7dc352907fc980b868725387e98aa85b0360aad7fe97789d260b3d8f49f"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9819aa89182b495ec976aded92a4941065", "guid": "bfdfe7dc352907fc980b868725387e989b6fc3b7d842c479e8e81487882e6bd5"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982f7ccffe2104cea89d3b16cb1d0c059f", "guid": "bfdfe7dc352907fc980b868725387e9857124f7e2276e95d4b99a72ffbc0819c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98197275869e8e6629ca51b9b2acd422b2", "guid": "bfdfe7dc352907fc980b868725387e98e869579647d6fd87793dd055e33c3494"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d2572ff1013f37e73699db20f42dea8a", "guid": "bfdfe7dc352907fc980b868725387e98234beadf1fef3da37bd52bc7b52f5a79"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989bf37d6a356b86b3ab392b1e035f44ed", "guid": "bfdfe7dc352907fc980b868725387e989e0fa97cb40f5230f009aecafd5f4370"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982bf50b249767303826c79ab0368fde25", "guid": "bfdfe7dc352907fc980b868725387e986b2659f7be54fefd204b5618990bab21"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d482d515cc075108efdc41df1f834989", "guid": "bfdfe7dc352907fc980b868725387e98531305f69dc2da1b9cd69e25c7057bd9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c706361fe7d9d145ba48ced3ca0934a3", "guid": "bfdfe7dc352907fc980b868725387e9893b2926f27fd96adfb4e30dc98ac737e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986820acf37e10b558e84568665e41a683", "guid": "bfdfe7dc352907fc980b868725387e98f6671bfd5e6cef6762e95496686fbefb"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fc8c14b7f3632b62a4aaf4916b96a8c8", "guid": "bfdfe7dc352907fc980b868725387e98a7f671c60fc57506e21eb666c5398ac4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9809424b6b0f63d82a623252fc5e14cdc8", "guid": "bfdfe7dc352907fc980b868725387e986dd7298360bb4da045b7e15490cca1d6"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98bbdbb76eb43228478e55b06b2a772f90", "guid": "bfdfe7dc352907fc980b868725387e98a853149765212baca85dd6378f58b9b4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98cc6211c3077edd5e4283f61f9e8f2f86", "guid": "bfdfe7dc352907fc980b868725387e9839712c44d33d112f55aac0c3f7e6f515"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989409440ddc6cdcf6f6e9cc492a03bdcf", "guid": "bfdfe7dc352907fc980b868725387e98b93e5eb7d9d9707a610b13c7462132db"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a6a3ead7f01fab08327d76a7002b2449", "guid": "bfdfe7dc352907fc980b868725387e9891a8655bdccb6d59945bc33565bb25e0"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d340e44c6a706a4c2d8e6f35e16a3259", "guid": "bfdfe7dc352907fc980b868725387e980b13add7ae4889608a78ca7f45d0d42d"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d9a2006768a961764904d6150b1ca3c7", "guid": "bfdfe7dc352907fc980b868725387e98f064210af1d9e0326a83f1a701d422cb"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e987f7bcdfa30ad46f8091434609f5b213e", "guid": "bfdfe7dc352907fc980b868725387e9889939209963e6bf7fd20fab13a205a15"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b32c8776e25a70871442e3a8877284c3", "guid": "bfdfe7dc352907fc980b868725387e986daeda4d013fd59eef1e5cd8596f7d2c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983a6c0c1395eb6124d6dd67af556f62a9", "guid": "bfdfe7dc352907fc980b868725387e984820f8f7cf6538476639a3b4ffb1a202"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9831b9c90d160c58c53ce77077fc3457f0", "guid": "bfdfe7dc352907fc980b868725387e98679c17d28194616126a6525652cb25e8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9859e22a887208dbe5b167f0ee5390588f", "guid": "bfdfe7dc352907fc980b868725387e98e3ab56dfdcf89edfd13891554fe28756"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a0c7082ba3bc3f10fd96798a64589821", "guid": "bfdfe7dc352907fc980b868725387e98250955e07048911f05cdd21a973af419"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989d6c80bb28e15520bf16273f26290b71", "guid": "bfdfe7dc352907fc980b868725387e98c2a65d109b4634e6a6639584a0123f08"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c623e9827afc54fd0104f0da4f8df9b2", "guid": "bfdfe7dc352907fc980b868725387e987c3c4788a6249884aa9084bb710f3a3f"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9816b73477c241cbb81539d1c99aa917cb", "guid": "bfdfe7dc352907fc980b868725387e98a59bc18f056097de340e2f359dbc8a9a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b98d2b88909a600479ca20bb2c5e7bde", "guid": "bfdfe7dc352907fc980b868725387e985b6fb64eb5a40f28f8c877658b8279e0"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9869ef2e4aa593407bbce1d95a40074bae", "guid": "bfdfe7dc352907fc980b868725387e980f5dd28f93ee642e59a186bedf132067"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fb29b67e4d38a371e0f449b97698be24", "guid": "bfdfe7dc352907fc980b868725387e980fd16e30d20d37c2995bad7d4b5b2179"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983561c337381894b8022f954363d0c25b", "guid": "bfdfe7dc352907fc980b868725387e98e1b8057f17e8d807a5b5332775c8145c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98736c6162c1efff1aabc2fee44eac3420", "guid": "bfdfe7dc352907fc980b868725387e9897238ac1a524fab48edfba02ef8d6692"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b3a6512ed552ad82c744ab1adced2f67", "guid": "bfdfe7dc352907fc980b868725387e985ff8d81d3c2f2f622d2d19f72a2cff0b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9877e003fd10825db4d5a7c468421f582a", "guid": "bfdfe7dc352907fc980b868725387e987eb47b95c8d9f90c36b571e274eae98f"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9814358464e5ef7156ad9cf2977c27ba30", "guid": "bfdfe7dc352907fc980b868725387e98fc14aa052b771abb3ca02233b15ed14b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9897f687484dd3d6c85591f57261b8f1bb", "guid": "bfdfe7dc352907fc980b868725387e98b54b1e68faae5e7eb2f1e022925232b9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b011ee2e4633965eaf2f3ea8299f11af", "guid": "bfdfe7dc352907fc980b868725387e9830899a36f34b128897853a24d1e9dfd1"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98674163fec6957bcc41eeb7de3ce5d0be", "guid": "bfdfe7dc352907fc980b868725387e9853ee35c19b9c362afee4de31f14f59ab"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988427569325e8b5d25fb0e1b6b79f483c", "guid": "bfdfe7dc352907fc980b868725387e98ec7198d5ec57bbc0726a88c402abe814"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989ab648a65823622459474aba8f41ee01", "guid": "bfdfe7dc352907fc980b868725387e9819db4bb71f1fec7b460d351d65a773e9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98703eda199fce891191dab226cc62bb8e", "guid": "bfdfe7dc352907fc980b868725387e98c0dcd8b00231d785e73deecdc7b3c9c2"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ab5053e781b3b47063ae596ca6712c12", "guid": "bfdfe7dc352907fc980b868725387e9853f7b6e0f7e1ddbfa7ff22508aa34628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830dfdf7465af67036617f21491be9572", "guid": "bfdfe7dc352907fc980b868725387e9833b40e60f495eee0eac305bbdfcbb5bc"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e985e63d082228a804b94709109314964ec", "guid": "bfdfe7dc352907fc980b868725387e9861b9a8ce86b09d7ac1ce11ed1ec733a8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989c9f739b269318a936253958ec01eb63", "guid": "bfdfe7dc352907fc980b868725387e98ee9441821671d77507a8996773ea25a9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9852b80a8b256e3b3f48f416bce63d9b4b", "guid": "bfdfe7dc352907fc980b868725387e983c136dfc9b194b885cbc350461f6217c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e984aff3970d076a4c5b6e5507e5332381d", "guid": "bfdfe7dc352907fc980b868725387e98d6a313f989dce706f84acb11fda5eab3"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e333863803c20193754b58dde8a3db02", "guid": "bfdfe7dc352907fc980b868725387e985a75f5f1a9535dff4e8bcd0282cfdb09"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989e27fdb54cba760211d82ab6d2e903db", "guid": "bfdfe7dc352907fc980b868725387e98fb79a3e6133cc09ab3a116cc0d7c91ca"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9809e26fb071b830d4a1dbfdcac55bedda", "guid": "bfdfe7dc352907fc980b868725387e983f984d67f788657cff3eba9616ef161f"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986e09c3228faf499e178bc3ce7b0dfc8d", "guid": "bfdfe7dc352907fc980b868725387e98250c1277f51ddba72cd3ca004519d56e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fa746e6663c694b02d342750239174a4", "guid": "bfdfe7dc352907fc980b868725387e98191f6880f4d000c435c96f5bec68ed44"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9863ecdf8a45964256a378b48afc335f12", "guid": "bfdfe7dc352907fc980b868725387e9867ecadc103046380c2febb0353fa3215"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98290c357549e786659848906549efcf41", "guid": "bfdfe7dc352907fc980b868725387e98857f539a28a4e24dc5d96feea328d16c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e981c2186b1df067a0c00018f8846a05132", "guid": "bfdfe7dc352907fc980b868725387e9806db0aa934b2e2dddb1eeab806937303"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9840ccbd7c059672984deba1e356e9b9f2", "guid": "bfdfe7dc352907fc980b868725387e989d58ee9001b5032ee3d8340253a7ee32"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986281b06c2be49a84b756e8d5c79835a7", "guid": "bfdfe7dc352907fc980b868725387e987e212a44054885ff7516417b423fa5fb"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980ae8121e9f6d19635d31d643f64add18", "guid": "bfdfe7dc352907fc980b868725387e98a467adceb93525088bdcc673c545eb57"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982847ab8f304e57394ce5c4b2e21821db", "guid": "bfdfe7dc352907fc980b868725387e988b0af17e976328acb4ff6e7a0cc59df2"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98873aca2ef90eb0d2e615af0892abf46a", "guid": "bfdfe7dc352907fc980b868725387e987ead810c2ad50ca84d068fbcdd68b9eb"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e987267d1fbfed73cb98066fe3804e02168", "guid": "bfdfe7dc352907fc980b868725387e983381bfb09751ff2da1a5226fbcbd313a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982685f23dbe816f6aa980c3c175674cad", "guid": "bfdfe7dc352907fc980b868725387e98b9aaae44e44b77b977f4f614888eebce"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98485d0e539536b8ec913645162bd006a3", "guid": "bfdfe7dc352907fc980b868725387e98a4226b146bc56069a986e87c8fbf5cfc"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9808bdffad267bd54045235e4fb0cd01b5", "guid": "bfdfe7dc352907fc980b868725387e98f271ebc0ea89c727510a135ffe3065c9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ebdf4f0b106a42712e810291d041c177", "guid": "bfdfe7dc352907fc980b868725387e985c500c9e2f814fe4032dd14fbdd5f4ed"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98aea97fd8bb5e851e994e8b4da564735f", "guid": "bfdfe7dc352907fc980b868725387e986a9026e09a15c3f435646c238884dfd9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9849a5b7f31ddb0405c23f73f735afc59f", "guid": "bfdfe7dc352907fc980b868725387e98848045dcf953696e5d467748b6e37a48"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e17f6ee8859509ba84a862613a681f44", "guid": "bfdfe7dc352907fc980b868725387e98227e723f3d4d56ca2b248af13e468f21"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e1cfd8f0ade2a7d7c940460394ec5f1e", "guid": "bfdfe7dc352907fc980b868725387e9864b61e18b5c06520a02fcdef16669f0a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a9e0528f6b3b8d25150c18d5a011cb58", "guid": "bfdfe7dc352907fc980b868725387e98fcfcaa0b5426e36d48ebefe4ad4648cb"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983e01edc517273298b4b05953595b7eb6", "guid": "bfdfe7dc352907fc980b868725387e983b5a525ef25ba01b9d98b8c80d1b364a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ab1d76cbc9667cdd0e39258f5d76fe88", "guid": "bfdfe7dc352907fc980b868725387e98e146f5fd22a2dd51bfb8752b23d66e2f"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e5fa47a8541ab13dde863eb200577bf2", "guid": "bfdfe7dc352907fc980b868725387e986a9ab8f2e7f5fcfdac6b2caca7c43bc8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989efabd4dbef433147a9f3a8874dc69a1", "guid": "bfdfe7dc352907fc980b868725387e98407a21027bab11fd4f1e0d88858dcca2"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e985b90e98363338b39bc086635f16a03b7", "guid": "bfdfe7dc352907fc980b868725387e98c089017bea3edad68bc011f08903e8f8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9893fd46e1f9a0ed567857cb071529d6c7", "guid": "bfdfe7dc352907fc980b868725387e9880ee18a60c1f62a380ee75d7f340acd6"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9812b989f0716415ce105513a58a381967", "guid": "bfdfe7dc352907fc980b868725387e987dab9152e07a71dbd7b2a2c73e478d1f"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9862a2e80338f824cab6a9c6639d839b79", "guid": "bfdfe7dc352907fc980b868725387e987924bb8f1c4ffc562b266b3936558115"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98aadcc97d944c9e7ceed031453b5b3d6d", "guid": "bfdfe7dc352907fc980b868725387e9808c844378c5910a0e5a5da6bc12ead35"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e985f0cae78b75376ecc10bbc2057670a4e", "guid": "bfdfe7dc352907fc980b868725387e9852c6fbc5343ac8cf85a5311d0e7740b1"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9826ae99237d0ea1c0a39fefabd0c7af99", "guid": "bfdfe7dc352907fc980b868725387e98eab55db9cd5b6c8a82316c77938c83b1"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a8bafb46848ec62ddedec5feb460bb0e", "guid": "bfdfe7dc352907fc980b868725387e98eb17a521f7d36b64c0a20ec376920ce0"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98522113f060dd04c4cb79315d5764188b", "guid": "bfdfe7dc352907fc980b868725387e989eeb811f446ae786b57a9c589f7efd79"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98656f8e6d619a4411398b5d4f38046e4b", "guid": "bfdfe7dc352907fc980b868725387e981c81188245be6cda26c6cc1e55b4b526"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b30f3cbb84b0d9e7f70866b5367f1d51", "guid": "bfdfe7dc352907fc980b868725387e98e5740df3742501cf44caead1c2974571"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982a73df5338b1805037d6dc80126e7b8d", "guid": "bfdfe7dc352907fc980b868725387e98bb301c4c1fe30a03e4171308a62748a3"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e985447a6e04847183ddd429aa1fd637255", "guid": "bfdfe7dc352907fc980b868725387e98ba3d284710e981fef51743e40bef8bd8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988adcf3a8e3b3bcd3c941751140e796f8", "guid": "bfdfe7dc352907fc980b868725387e98eeae63c316e60e2e15cc3330180d22db"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98db0b69f1bc46b01410a6ad28dab54e1a", "guid": "bfdfe7dc352907fc980b868725387e9815d035b64214723b55c683af35abee94"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982657e720152dfc6d019010f4e763a4e5", "guid": "bfdfe7dc352907fc980b868725387e98bb1c7f990637d6d46fd46f989456f374"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ef6bed909dc162cf2531a20f26463248", "guid": "bfdfe7dc352907fc980b868725387e9812a417ad60676d7459849430b4aace16"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e985847f813c307ea47fe07088292d2ca1e", "guid": "bfdfe7dc352907fc980b868725387e98a5efdae0b7af1ef865d9170961e37605"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986f5685f39b2b5c621f3e5998d4fe9fde", "guid": "bfdfe7dc352907fc980b868725387e98bd5b73b8b7880c5476ddecf0f06b2c8e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9823b52742997a449a96955f33cf7b05ab", "guid": "bfdfe7dc352907fc980b868725387e98ca503b0723fed780e9c74f02031e1e0a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98cf26caf481e26a2b650796ca7b2c318e", "guid": "bfdfe7dc352907fc980b868725387e982052ea3730206bb64e87c961e84153c0"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983c080b6e701a6195d868333efa9f46b6", "guid": "bfdfe7dc352907fc980b868725387e985daa87eb84520aedf8393facf6fce291"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9808c3d8f9b64ae9cb9be3d18ae9ca71c2", "guid": "bfdfe7dc352907fc980b868725387e98aff2247fe3fedfc5d35a0d6a4dfab5c0"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9849faf847d5f5c4560ae3a31a1115818f", "guid": "bfdfe7dc352907fc980b868725387e98086399baaa438594b27d7feae6818bd7"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f368eca01833a0912ed307c5d1768650", "guid": "bfdfe7dc352907fc980b868725387e98074fc2313fa9cc1151b6ce370360c1b8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9804156ac4423ce22304999c0fc0fd553d", "guid": "bfdfe7dc352907fc980b868725387e98bf94c47c0b37115e198bde0cd5ebd66a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9860a7892058838f7b9f393fe37bed21b7", "guid": "bfdfe7dc352907fc980b868725387e98671595e83f96d8d89cde45aa2e3d319c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9819843f4ba651af3fca13211a43009c89", "guid": "bfdfe7dc352907fc980b868725387e983e8184eeb240127910de548d0473297e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a3d4c8ac90bd2867540036487172f588", "guid": "bfdfe7dc352907fc980b868725387e98ce74c011f40ed38acfb408b97d7f042c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9885685bcd755118b75b6cb221cd6bd6a9", "guid": "bfdfe7dc352907fc980b868725387e984fbcba2e07145a7ce4f8edc6c77e9865"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e984aa07dda824a8370657e360d80c57c26", "guid": "bfdfe7dc352907fc980b868725387e9875ba161a194e857853d0249e908962f3"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9860b9df34546a8658923f169c9d612501", "guid": "bfdfe7dc352907fc980b868725387e9807639ee514db793f8acaf5f2c88037e4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9814af1c15c15227615a63e19a0b76ffdb", "guid": "bfdfe7dc352907fc980b868725387e98ba3c1c2029067780614eb929c94fc4cc"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98cbf0e9e73b8066b12b7ff561c26ccb80", "guid": "bfdfe7dc352907fc980b868725387e988466ed0739dead2e758518a961e48484"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c5d40f49bdc60ee1241a0fe8531df883", "guid": "bfdfe7dc352907fc980b868725387e98de7a5ef2596365eb5973672c4c3f0d51"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986ac28f44e2ed0d64812952ca04f46312", "guid": "bfdfe7dc352907fc980b868725387e988b524293bc701c5866d2d4cd86cbe2f4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9852a1cc776cc9af51332489ecbe033eb0", "guid": "bfdfe7dc352907fc980b868725387e98f77ea8cbde70779363135f59c61ef411"}], "guid": "bfdfe7dc352907fc980b868725387e98bd7938669f236851d8c1ac9286d6f717", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e27d661740f9dabd2aa31605d055f69", "guid": "bfdfe7dc352907fc980b868725387e98e49dbe2949e1178375f280e440504306"}], "guid": "bfdfe7dc352907fc980b868725387e98eeacf52519b9389b5edd3a70a12895b9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9887c6d76d5b1d706dadc6b67a2eba2688", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98459ecbbef4dbbe8a07a0c87bad0e0d1b", "name": "libwebp", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982988ca927fde17a51bcb980424f8e9e7", "name": "libwebp.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 1}], "type": "standard"}