{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d77fa3bbbba70dca6ca4742904553eaa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980dcb367de0e756bf866698b7ce7ad16d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d77fa3bbbba70dca6ca4742904553eaa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a63dfb529f9d7341bfb9ba916732ce90", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d77fa3bbbba70dca6ca4742904553eaa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db411cbd21fd417755d809327bd526d9", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d77fa3bbbba70dca6ca4742904553eaa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e7e0f656942a1510180f3fc17263adb", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1941e279b35bbf918e35dfc137ed655", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f0e0c71d93e3ae420643311a2908ab91", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1941e279b35bbf918e35dfc137ed655", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988afad30c305e0610e3762d43669bf153", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1941e279b35bbf918e35dfc137ed655", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c99950872203d7f0fafd702a51cf5815", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1941e279b35bbf918e35dfc137ed655", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9814ba7a9c3e3eaa972e06d2fb598bf67c", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1941e279b35bbf918e35dfc137ed655", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c6fdd4d1c3acd61608d8c91319eb9b9c", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1941e279b35bbf918e35dfc137ed655", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cef8f062e034d1e89cdb93bfdb655157", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1941e279b35bbf918e35dfc137ed655", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acbdcdb7118e2c822d0c232d1fcc7130", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1941e279b35bbf918e35dfc137ed655", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b45d3513275cc261b0beea56be00b862", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98358221049c31c9ca7d3231e5081bdd05", "guid": "bfdfe7dc352907fc980b868725387e98c29752261e29370f56a07c7c89944cdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ca4294c8532d76f3d3e1e62b996c8a", "guid": "bfdfe7dc352907fc980b868725387e983acbb12dfc1455f03673d39383e7d197"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847d0101b0f52fe6a3c786e01054d76f6", "guid": "bfdfe7dc352907fc980b868725387e98912443b9840e0d515910fed9a8bf4f6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d10d32d53aa2676bf7d8ce10ccb70570", "guid": "bfdfe7dc352907fc980b868725387e985152205f938b5a5dc7310fe756d31c3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3cb0247a4ab3fdee3ca68dddcdf0e4c", "guid": "bfdfe7dc352907fc980b868725387e9842ffcea22f3c0ae1f2832f638b9505b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98900f84f901e63843eaccc2a4c0cba8bc", "guid": "bfdfe7dc352907fc980b868725387e9829ba5ea1c784262a48a5e034f0cbfb98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988daa8a276f9f9e3bb8855d8d119ebc21", "guid": "bfdfe7dc352907fc980b868725387e982c9589a642c909441ed6bd247f109789", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855469b3aaabc74237b29db28dc5193c1", "guid": "bfdfe7dc352907fc980b868725387e98e2cbd5ba60880cad02792fd42139e0ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846e3f086c0fd0715876b3b5fc19d0eeb", "guid": "bfdfe7dc352907fc980b868725387e98d567039b640015b16c7d86e5a34d922f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a9da85ce1cec4a7c23b08f62d62de2", "guid": "bfdfe7dc352907fc980b868725387e989dd1e3e7ad5f928d0ca2e2cbdadf2743"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c05ec603b2673e55fe80385ef14efe82", "guid": "bfdfe7dc352907fc980b868725387e98e59686555a491b5a1510f247aede45f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb4a1ca61898a88964b8ad747e3b287", "guid": "bfdfe7dc352907fc980b868725387e982026369c82120edbb49d5e94386a5a5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838b49613215be73ac879b960c37d8b00", "guid": "bfdfe7dc352907fc980b868725387e988cfe4967a92ca41bae58002341b06875", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c372691a1f31cee125d2fcf31b90bfb2", "guid": "bfdfe7dc352907fc980b868725387e98166b4af361ad569e692310888650b8f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e83e952649749c570963865ecfd10451", "guid": "bfdfe7dc352907fc980b868725387e98667c2f022c9ad2a5beac57ad4b66ffcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f44bdd92a3228372b4edd9863be58db", "guid": "bfdfe7dc352907fc980b868725387e986a427c139e8d8eafe77cae1004779f74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985092b28229246bb0bea183ad5a6a7928", "guid": "bfdfe7dc352907fc980b868725387e98e76292c4efc4737549bf5b79545b16dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98211d72a01a703e9c7f2229325a6bf886", "guid": "bfdfe7dc352907fc980b868725387e98673fdb78e3aeeaf187e6971c45f98049"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819afa0184d8780704e2164378ba646f1", "guid": "bfdfe7dc352907fc980b868725387e98dbac7198c311fd3513e0c964bffe8a3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b389b0ca6ef6862a5c367471860ff62", "guid": "bfdfe7dc352907fc980b868725387e981d62d9ddf9bc32d823d355e9461568a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a10d8eb50fa7bd8d0f6cd84ea16c0ac8", "guid": "bfdfe7dc352907fc980b868725387e98e940a1150ee8f4736506e391ff45942f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daed74d86c43f6d32d6d0af2d9f7ff28", "guid": "bfdfe7dc352907fc980b868725387e98c72e27c9116a06ae07ae62b7a80131c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1a287c127bdffd021a4a1af4e9120a5", "guid": "bfdfe7dc352907fc980b868725387e98c7ca186b63e8d00af9e040f34cc2f133"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec747cafa955beef7135275977b87601", "guid": "bfdfe7dc352907fc980b868725387e98c69af376020989eb5d1ab73fdb07d3aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8e373485a100a960dfbe5d19588dc1", "guid": "bfdfe7dc352907fc980b868725387e98f4dc39f341326e7a0c1e7e1cd442d8e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b377a343e5094bf4cc2820f94c231dc0", "guid": "bfdfe7dc352907fc980b868725387e98a978e5590a8a83dcaddd5323cc6c254f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab9853ce2587111eb211a4e93b6d2663", "guid": "bfdfe7dc352907fc980b868725387e988fb5cd036d1cd6fc2db15297c9e639ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7cfcb3fd730ee2ec5648c6440dae3e3", "guid": "bfdfe7dc352907fc980b868725387e98e3accce089a492ad50bcd3a3d7b6f5b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5e5caf43b3013b8a2615f37d32b8715", "guid": "bfdfe7dc352907fc980b868725387e984e204f4ab866c1120630bb6e79e771db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871bece2c45190d3da2f98c144d18bdcd", "guid": "bfdfe7dc352907fc980b868725387e984933f569e9184b22495e8f5dbc5b47cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fa72d08ff95a96e68f880aa89f0dd65", "guid": "bfdfe7dc352907fc980b868725387e98f7eec5fb0dbb997f7a26769ff5869bea"}], "guid": "bfdfe7dc352907fc980b868725387e987119dd03c281e7a9bcdcb45d2482dff0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98610d0056db6b7e1a55c0a690507d95ad", "guid": "bfdfe7dc352907fc980b868725387e989ba2619af0a20aba92c8f60eda896612"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847495dc1c4aa396fad37e71a3c0637a8", "guid": "bfdfe7dc352907fc980b868725387e98c4dc39b4b773a3d5741de912d1088aaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eae5cba6ad243ecf4a9e085b166588e", "guid": "bfdfe7dc352907fc980b868725387e9840dd978bb07e7855dee98b365423dc28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ec717f33e707dba90abb674cfff10b3", "guid": "bfdfe7dc352907fc980b868725387e983f68681cef66ab838ab2b165916f94d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e0cf8981dda679553ca72504fb2807b", "guid": "bfdfe7dc352907fc980b868725387e98152432457ff6dffad6941cb17788ba62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850c378511877a8005f02b6e6aee7966b", "guid": "bfdfe7dc352907fc980b868725387e983757d43f8a4e63012ebd6d26173382b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7f9c46f3706219b4b3df0fd0a698f0f", "guid": "bfdfe7dc352907fc980b868725387e98c57e757874c9f5bb286e1756246bfc7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989221a0c063ed9c6b051da2eb34908535", "guid": "bfdfe7dc352907fc980b868725387e985d478c283ba999966834cc7e76ca733d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4e302ceb91a220b0f921954a0e497fa", "guid": "bfdfe7dc352907fc980b868725387e988723fcc6e30a8af05c58b417424d5dc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98affae173dd94f356bc97387fecd01bb8", "guid": "bfdfe7dc352907fc980b868725387e986a1d83a9383c31b17d15b088f79e81a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98649452882e8f36a328e008e92d446ac6", "guid": "bfdfe7dc352907fc980b868725387e98decfbd7d92408fe1cdac4f4caed586e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987231d8b3c318346061c91e1b216c6309", "guid": "bfdfe7dc352907fc980b868725387e98e9f8269afaba52afa600cfbc9ab720a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed7f646bd06158b218a94f662bfa8eeb", "guid": "bfdfe7dc352907fc980b868725387e98f60281ca17eeb4e44acdc3b90243c0af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fbe179a12013da995aaa777cd77de84", "guid": "bfdfe7dc352907fc980b868725387e98a18013207e38e264fd237d306c24974b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e52115cdfeaf14de439534610cdc7bb0", "guid": "bfdfe7dc352907fc980b868725387e989f6ee08f53d8dae1918bdf330e955255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca11c3623b3732a7030ff92a74f4a5ee", "guid": "bfdfe7dc352907fc980b868725387e98515c3a10dc4927ffba289653ee46c26f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98869027739aed2ce785b768328a11a5a4", "guid": "bfdfe7dc352907fc980b868725387e98fd05533b9625cfd4c2cf29d76d8ce7f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832f83754de91e0f4e5fb7b76967bd8e7", "guid": "bfdfe7dc352907fc980b868725387e98d6c69c60480c790a4b36abb5a0a8aa46"}], "guid": "bfdfe7dc352907fc980b868725387e98f5f40f0ad5b7c3b0bd78616f3bbe770c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e27d661740f9dabd2aa31605d055f69", "guid": "bfdfe7dc352907fc980b868725387e98b90319c084d2ba69c6b43626cdd2f8fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807e734ef3e50e9360b339f4a261e0219", "guid": "bfdfe7dc352907fc980b868725387e98ce132b9058b071232b78f0a7bf9e9038"}], "guid": "bfdfe7dc352907fc980b868725387e988860fa2c6712e3429ee616fd5b07d892", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a0c1d6c25fd10bb01932f11f9b6d4d92", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9883291697b003650da7dbb65c5d98328a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 1}], "type": "standard"}