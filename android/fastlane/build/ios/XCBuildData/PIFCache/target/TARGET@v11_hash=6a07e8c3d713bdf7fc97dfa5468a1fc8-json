{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9893ec49ab6102843175e486e21007615a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98cce9214e7b134baf7c3fed59ec1726eb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9893ec49ab6102843175e486e21007615a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98aa300d29121f4daee6c30f95a32e6a42", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9893ec49ab6102843175e486e21007615a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9827a7f47ee35e60932fb458ae52d81cba", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9893ec49ab6102843175e486e21007615a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9802fef44b057c5fd51d716e8cc782fed7", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98372dcb1e4ef8735a4a2f8c0ce21dca38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9805d361cd16423dee8926091d27ed016e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98372dcb1e4ef8735a4a2f8c0ce21dca38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b88cf77355590154f68b76c94dec946d", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98372dcb1e4ef8735a4a2f8c0ce21dca38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98fae6c8d45ec3eee75646fb7cbfd1ba03", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98372dcb1e4ef8735a4a2f8c0ce21dca38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983b368337b59fbfce8f0253dfde5bbc92", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98372dcb1e4ef8735a4a2f8c0ce21dca38", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9822cf6023f5f57ec9e3f4865075bb430c", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98372dcb1e4ef8735a4a2f8c0ce21dca38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981a84224d59f6bc29653448730e617dc1", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98372dcb1e4ef8735a4a2f8c0ce21dca38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983dfd2e4e6abb7804dceda20db33a00fa", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98372dcb1e4ef8735a4a2f8c0ce21dca38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/in_app_purchase_storekit", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "in_app_purchase_storekit", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "in_app_purchase_storekit_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e985bb943360847cfae5f6f71c37d676359", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c32c6cc4dd45098a505f0dac48c35695", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980736c2c036168e364a29e7291a04810d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d14116c2b431e37c579ff6888f1dc8d3", "guid": "bfdfe7dc352907fc980b868725387e9879650221d03c4f083451c7e6e78d1b13"}], "guid": "bfdfe7dc352907fc980b868725387e9858683adfd49d59012eeddeae736a8ab5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9", "name": "in_app_purchase_storekit-in_app_purchase_storekit_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98660111f54b33cd47ecb8f2e17a16b740", "name": "in_app_purchase_storekit_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 0}], "type": "standard"}