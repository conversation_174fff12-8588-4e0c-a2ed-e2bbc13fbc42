{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f69c972958d4a6922251ba7c86da0bf", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "ONLY_ACTIVE_ARCH": "NO", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98b546bb35849749d9268fc38bb07c2bc4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f69c972958d4a6922251ba7c86da0bf", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "ONLY_ACTIVE_ARCH": "NO", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98e7cb25c3c36523dc680497e5b0affc31", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f69c972958d4a6922251ba7c86da0bf", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "ONLY_ACTIVE_ARCH": "NO", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98c2e01adffe7e449db436d12c7485b5cb", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f69c972958d4a6922251ba7c86da0bf", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "ONLY_ACTIVE_ARCH": "NO", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e9880240505ebe8639c82049c6664795761", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849c3f68cd38d7e8917f53d378072f0ae", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e988e80213764ac4b9814d15ffe64c02abf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849c3f68cd38d7e8917f53d378072f0ae", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98167dfa26977dd1f4f58ebc12d04b5f7a", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849c3f68cd38d7e8917f53d378072f0ae", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e985a5f96e5cd168748d217ddd41bc71a7d", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849c3f68cd38d7e8917f53d378072f0ae", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9867706d2f123feb8545cc67ee29c90618", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849c3f68cd38d7e8917f53d378072f0ae", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98769bad5fc02b327c562866516b6d9cd1", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849c3f68cd38d7e8917f53d378072f0ae", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e987102b0eb74cb08567235c3751e2c3a3c", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849c3f68cd38d7e8917f53d378072f0ae", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98fd5b7d7c015bcbd9aea188a730534053", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9849c3f68cd38d7e8917f53d378072f0ae", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e981895db355bf7354c0fb6851f654f3623", "name": "Release-staging"}], "buildPhases": [{"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "false", "guid": "bfdfe7dc352907fc980b868725387e982aa346f98a00154681bb93df4252c48c", "inputFileListPaths": ["${PODS_ROOT}/Target Support Files/AgoraIrisRTC_iOS/AgoraIrisRTC_iOS-xcframeworks-input-files.xcfilelist"], "inputFilePaths": [], "name": "[CP] Copy XCFrameworks", "originalObjectID": "70B647D4AB29837FFCEEA0B9A63A8D1E", "outputFileListPaths": ["${PODS_ROOT}/Target Support Files/AgoraIrisRTC_iOS/AgoraIrisRTC_iOS-xcframeworks-output-files.xcfilelist"], "outputFilePaths": [], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "\"${PODS_ROOT}/Target Support Files/AgoraIrisRTC_iOS/AgoraIrisRTC_iOS-xcframeworks.sh\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98a25b5aa0f2f10b21dd713bc93ff6ab56", "name": "AgoraIrisRTC_iOS", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug-dev", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug-prod", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug-staging", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile-dev", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile-prod", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile-staging", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release-dev", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release-prod", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release-staging", "provisioningStyle": 0}], "type": "aggregate"}