{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981cfa551285ce7b3ce7f3c20470772c4f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9833ea9a57aae202488d6e73e161e58b65", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981cfa551285ce7b3ce7f3c20470772c4f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982be7871522b9d1745dd66b3d07cb0783", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981cfa551285ce7b3ce7f3c20470772c4f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9844d75e81ec8fddbb03fe774a65f295ee", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981cfa551285ce7b3ce7f3c20470772c4f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980acdc3b852c5cff5572ec9cf3f7c00af", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c369e3f4b1e8cea1f26ae2a64fde043", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984573c586ba0219df59c95ed8d4e06c29", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c369e3f4b1e8cea1f26ae2a64fde043", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98935efdcc9b658a6a2f704dfe197a95eb", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c369e3f4b1e8cea1f26ae2a64fde043", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b72aa6587fb02490c22c8002109a46a6", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c369e3f4b1e8cea1f26ae2a64fde043", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a78e5145187cfe025a27da181b54fca2", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c369e3f4b1e8cea1f26ae2a64fde043", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b422b38b964673517d2a36db51ef90e9", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c369e3f4b1e8cea1f26ae2a64fde043", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980a6e133acba312a986c7f4e3c2f05059", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c369e3f4b1e8cea1f26ae2a64fde043", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b22c8526e00a8267d9e57081917aac3e", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c369e3f4b1e8cea1f26ae2a64fde043", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a1336506b4a9238864f4c1f12746b74e", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982c3a74b43d7a08684b3e5091e2a65b05", "guid": "bfdfe7dc352907fc980b868725387e989e771256850b9e26ab052cf1e17bf4e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d5769d562e369ab7d33d1ef49e8ad20", "guid": "bfdfe7dc352907fc980b868725387e981ba81f5eaef46691d89f9b581b76e1f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d28d34066bca78f48737b67896736f2", "guid": "bfdfe7dc352907fc980b868725387e98bf4f8ba8b74a427a11c05e0b03733763"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f6b09f30373623503200c66c9357ded", "guid": "bfdfe7dc352907fc980b868725387e9889997fa69a5ab1dfefa6c38fdd273da3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983642275828e5d47613a4496bdc39d9ab", "guid": "bfdfe7dc352907fc980b868725387e98d83fa863c89c507fa9c955635fba6eb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a91c35c766d672cab397ed80ad1c3cd", "guid": "bfdfe7dc352907fc980b868725387e98eb2a1cceec272f3ce28d4dc51d8c269a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8f5f765fb5f64c148513d497b699ddc", "guid": "bfdfe7dc352907fc980b868725387e98dabe6971cbb385890e92d3e15dca94f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e60ecfa4e138bb3545784c8ff147c531", "guid": "bfdfe7dc352907fc980b868725387e980a104c8513d0e1f7baf637cfd45f2b59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869e2e36f5a8f803678c4d00d3bf42fc2", "guid": "bfdfe7dc352907fc980b868725387e9870c8ee98b9ca2f5e4fd5770bdab60ed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb038ce48dd1a17c9abf48404964cb26", "guid": "bfdfe7dc352907fc980b868725387e988ce6c5826fad2a37a1d80302dfec9442"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee3acf14fab1d72ac3015d76e316e54", "guid": "bfdfe7dc352907fc980b868725387e988a8e4ffb1bf79c00e0cd70f0df9f5825"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a96a66441c10b8eea32568518579f1ce", "guid": "bfdfe7dc352907fc980b868725387e98908b70ef13d78acdc40c4f583fbcc373", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859a5820a7c674463c8aa82a0fafe9281", "guid": "bfdfe7dc352907fc980b868725387e9838ec9c0dfe2acb3cb3a55fee77b87afe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bdb81926b436a970983c576f97229ec", "guid": "bfdfe7dc352907fc980b868725387e98512c204798a5c3544a43bd1f4f1f7f79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f96f8e8c63b8657608c2d1a08b81d26", "guid": "bfdfe7dc352907fc980b868725387e98a92af0f2e070f98fc119fadaeda738f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e83683930f66820cab82f9a9d2e38574", "guid": "bfdfe7dc352907fc980b868725387e98e6781fb537877f448e334d9d85926e6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956dc4524277dc6a3d863dde863e3f2b", "guid": "bfdfe7dc352907fc980b868725387e988a976a13a692d49908f36c5bd2996b7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b7ff59ef5ed18c38c9e002e5697d4bf", "guid": "bfdfe7dc352907fc980b868725387e98912899efdbc3d93dc36798a4647ba3bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98512381ec05610aad74104f2bf8554c35", "guid": "bfdfe7dc352907fc980b868725387e98aba013ada69c8f06bfd152705337717d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a14c2231657edfdc4d257ff42ecd7d7", "guid": "bfdfe7dc352907fc980b868725387e98d8d3f624e83f9297234d0734e720ffc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98508f0f40a9981710ca1534b2c4765e5a", "guid": "bfdfe7dc352907fc980b868725387e988330b2586b7fd77c6036ac553c00027d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebb4fe6a355cbba677c2bbc3794b9b5c", "guid": "bfdfe7dc352907fc980b868725387e982572b496ec0acd8d5fb3981bf922596c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd983dbdfa6a521ced9410fc71e78cd", "guid": "bfdfe7dc352907fc980b868725387e987cbe629b53c64920e3e9fec40cd5c7a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873def6828ea2b1176628b6b4d6e66bfe", "guid": "bfdfe7dc352907fc980b868725387e985e168cf6b6042d3ba024bdf20dc4b512"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897fa78c6e3b5bb4dafda43504d493f49", "guid": "bfdfe7dc352907fc980b868725387e9848421d7a12647db03b199f1670f888a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c985a6805206f1a0c267d244d57f50df", "guid": "bfdfe7dc352907fc980b868725387e98c1465a3b5cdd770ebdd3566ce8b75c6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ef46847d1377feddbc43f6e0fc9883f", "guid": "bfdfe7dc352907fc980b868725387e98c1b9cc64bc066facf6bf542e884eee9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcc704591610c84c42473ad2f9e9518c", "guid": "bfdfe7dc352907fc980b868725387e983cbfd7c078f7510ad20a9d18b32120c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e9d7862f3285f0819ef1020b9ebad2a", "guid": "bfdfe7dc352907fc980b868725387e981bc8bffcfd790fbf2c985a16de729eb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cb3482f7a231cbd5fb5dfda648fd245", "guid": "bfdfe7dc352907fc980b868725387e988bea4cd556741fd4928d17a41573a610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b176bb6294238e2b97eff2dda9e0221", "guid": "bfdfe7dc352907fc980b868725387e984f8fba1dc104769f5d199e9025af8598"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b67924272dd017f12f929e4ff5d37ae0", "guid": "bfdfe7dc352907fc980b868725387e98663a1490fa821b72fb0a75e90719fda5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98327d3113a7f9bdcf9f185d1ebd24f429", "guid": "bfdfe7dc352907fc980b868725387e98ba6e642a3eee21b6c753251f8a2243da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858b889438b4e7aca80868418bba95766", "guid": "bfdfe7dc352907fc980b868725387e98e495e6281921c63e129e04de7912d371", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcefde1a3e79d243384ea1a1c8395df0", "guid": "bfdfe7dc352907fc980b868725387e986db28bd6e7266961fd189952aa51f7d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e481ba357138c8b97faf407c6699df6d", "guid": "bfdfe7dc352907fc980b868725387e985f16b8d278b3756d71980d25bfd39f46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ea2ad305b19beb5ae4ab773b7f4a9a6", "guid": "bfdfe7dc352907fc980b868725387e98b536d8c6a9aa2c25cda9df9912a2debf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98791b1702970cc96071e4a1b6d8238a0b", "guid": "bfdfe7dc352907fc980b868725387e982f8e2a26485fcf44b7766ae4c9979353"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee55cf2b6772cc3a2d34b893a8a4847", "guid": "bfdfe7dc352907fc980b868725387e98c0c8ebae08e020202a810ac90da67f85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4308c5524bc16e040449c92f5dbd80e", "guid": "bfdfe7dc352907fc980b868725387e985abe1fe62b8fb4658cea0290d9cd3ccd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c93d3a5f275ebeebc2b99a51fe21d5c", "guid": "bfdfe7dc352907fc980b868725387e98701b28ab5c910f9f77cbe0ddd1e23527"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f03965e01a9ad1d6033749518be8e57a", "guid": "bfdfe7dc352907fc980b868725387e98097261ff63a05a1edc32530125a5d903"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981670989546e4c9c1e3c5d477dbfc7391", "guid": "bfdfe7dc352907fc980b868725387e9839c21735b4009b05f1ccd845fe082b77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e950fe46a5045ed135eecde0371a469", "guid": "bfdfe7dc352907fc980b868725387e983008ed5737b293f9e4eabbfd9319175b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b79ff32f4388e337c9d9a7c9e06e3f3", "guid": "bfdfe7dc352907fc980b868725387e983545945f02cda7a2106f567132b1e296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f093bac87a8cc03c9719bc05aa8493d8", "guid": "bfdfe7dc352907fc980b868725387e98ec23c12586bfd0a6493a8ccb91274edf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e8c9ee136b0f837f5cd2c58a5501646", "guid": "bfdfe7dc352907fc980b868725387e9865bbc20b550b6b25a7d1ade4936ff175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb801b91d5f52d02ff9441905701f683", "guid": "bfdfe7dc352907fc980b868725387e98acbbd954dad36d0c1fa243e0beaa573e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987652c8deef141e3026dfaad39719c10a", "guid": "bfdfe7dc352907fc980b868725387e98a4b61a7c7e1123b34f72dd949082146e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba906b16d8dcb97cbff16b8039b0d3a0", "guid": "bfdfe7dc352907fc980b868725387e989dc77970e3dc45b5ca53841489a237ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989425cfd32a547fde9689dc09442a1d41", "guid": "bfdfe7dc352907fc980b868725387e98b31ffcb25ec8bc09449952e1b13c66cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cf28ca0b4b7cf868a9a2dee24c4d263", "guid": "bfdfe7dc352907fc980b868725387e98343d0cdb05412ae0c05b152421b88645", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981a0537ac71d4dd619f9a169b24ef9611", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989b37797d56cb04903f14c0eee98461b0", "guid": "bfdfe7dc352907fc980b868725387e9890d18d66773ceef7d5aac02aea1c1ab2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98430fb1452cdce5dab4cbca06e7ff097e", "guid": "bfdfe7dc352907fc980b868725387e988a5b2d3809b76e4a1b1426b681a65967"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98888a431e8a492a409316a826137124b4", "guid": "bfdfe7dc352907fc980b868725387e9865cafadd8e15e125987c2832339f910e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985df6582e70d8689b1412eea0ae2ae2be", "guid": "bfdfe7dc352907fc980b868725387e98212555b907fab2e265dee762b383a768"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3c0024e74b70a32dd8f3e04162a6ae9", "guid": "bfdfe7dc352907fc980b868725387e98112c13566b20dc901f2519bbd7a75a56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893e9196d86eb406666b619791ab3151d", "guid": "bfdfe7dc352907fc980b868725387e9885d57ef6e6f1266329f493bbdf234b54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825cf4601cbaf5b3e96b1625fda67f03a", "guid": "bfdfe7dc352907fc980b868725387e9833cd88164afc4e43d191b5cb2f539316"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a649f16869486efaa70edf266698dd9", "guid": "bfdfe7dc352907fc980b868725387e98594d31a59fe5e512ca64fcbb9ca9ba85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868eb86b2e12d722e8620618aea0395d7", "guid": "bfdfe7dc352907fc980b868725387e98f41903df2bfeefeb5e302b4ea8f1ffd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a49912052529414ec68e1522ba6d769", "guid": "bfdfe7dc352907fc980b868725387e9859e728a082d32aed17a556eeb8cd45d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ce84ae840a856adb3284c776c69888c", "guid": "bfdfe7dc352907fc980b868725387e98baacd8ccb548c6efe3db4b987d83e9b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834ff04cd8afbfd994c3d7d6d740d8002", "guid": "bfdfe7dc352907fc980b868725387e98871b6655ac861a2d4adde1a3d9199ef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98502c7c72f02564a269b3fcc3401a2bb5", "guid": "bfdfe7dc352907fc980b868725387e98a35dead0a08706dd5bdbcd6b007eddd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5c7f58cff975f2ed979bd5607911435", "guid": "bfdfe7dc352907fc980b868725387e98aa77810e6872af14dd6fd64b186edd17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986516a717b44501e27d747092f28fdc38", "guid": "bfdfe7dc352907fc980b868725387e9854ffd14bac700258bcd8332aa703eefb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706001e09c7d3716f63dd2df14923f4d", "guid": "bfdfe7dc352907fc980b868725387e98a47efc243b8317fb3a7d0d7c6b6e036f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bc31a8b1b388a63f61ad8ef2c07498c", "guid": "bfdfe7dc352907fc980b868725387e98852a9b47feecea1b13c9fa9030fd76c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826bcfd73de9c8c4fee38591f25b95229", "guid": "bfdfe7dc352907fc980b868725387e988f2c5e67539a188263192b483362858e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bc262aa1473396e280b30fee4184bf4", "guid": "bfdfe7dc352907fc980b868725387e98c6cf5bb3591a1882c561d0c58b1b8c39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814e5eb2fd1220a7e1266e5e4949ca9a2", "guid": "bfdfe7dc352907fc980b868725387e988ffc68f4e2ff487ef8dfabeab6af2293"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c979b37aed773fcbf8332751338de85", "guid": "bfdfe7dc352907fc980b868725387e98e90669401f08149e589d288261cb1fdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fadf634c9515397c2f0ec67bf901b4db", "guid": "bfdfe7dc352907fc980b868725387e98a75382791e45c873fb2b7786a7e89bdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd5378ea8bd79a2c269700434f90b888", "guid": "bfdfe7dc352907fc980b868725387e98967dd5d9125aa76b5693cd75ded2e1ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988460af40f4fcbd9befd46ed92b35cebc", "guid": "bfdfe7dc352907fc980b868725387e98ae05474ca34da5ad35c42dcda4d7cab8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987adf2f139ec8171a6adca95eccb57590", "guid": "bfdfe7dc352907fc980b868725387e980de59bc4ef4d2951ec414d8f95f0b77f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc41d934f184df814eae7658ad7ac39b", "guid": "bfdfe7dc352907fc980b868725387e989946c942efde051a5260f6505f239606"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986edd1f2dceaf299b765de35b403ff395", "guid": "bfdfe7dc352907fc980b868725387e98db745dfba2ac4cca200662ad3465b5a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c13fd51e63e868ed5cf243519b723a4", "guid": "bfdfe7dc352907fc980b868725387e98905ead0506176911742ef156f599b406"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98132ad1afbff5a12d415e43bd3827837f", "guid": "bfdfe7dc352907fc980b868725387e98974ccc5f8a86a70ca342144ec82d3a5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab0016a839accaedf38a2ff2262f5887", "guid": "bfdfe7dc352907fc980b868725387e98973a4557079d4577139ca6eb35eef8ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d398acdaf4f6db8db978c22df1507159", "guid": "bfdfe7dc352907fc980b868725387e98722d541a26f16ba7a936fe2760a1ca12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dedf6577e8f296357df00bbe3f87c33", "guid": "bfdfe7dc352907fc980b868725387e98295365757ccc23be2ef2eb2dc40d083b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877d6b22d01d60b702f3cff5f59738f80", "guid": "bfdfe7dc352907fc980b868725387e988721e09dbb8f030ca383ab14aba2b50a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859e0fa423fec40306b352c20fe7ee6bb", "guid": "bfdfe7dc352907fc980b868725387e9878cd5a73d328905e4ea48c71b0da6ea2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3e3479cfc8cbcafc1de74a227a6bc96", "guid": "bfdfe7dc352907fc980b868725387e98246067d61a8a94bc7ded8e16c50e807f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b2a0579eb3e19f0119a35b0419a87a8", "guid": "bfdfe7dc352907fc980b868725387e98b36b9ee037ed14f24ad545b3437e650b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c96c6eb908863b752eeb8076fe8706b1", "guid": "bfdfe7dc352907fc980b868725387e9802be478d2a62335e3fac7b9cbb34f195"}], "guid": "bfdfe7dc352907fc980b868725387e988aa17ef99575729bf792d1e3f4416b1e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d845ff404c55dd0bba90bc48acd667c3", "guid": "bfdfe7dc352907fc980b868725387e988bf28db86a76a703a4bc0eb26b11e54d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e27d661740f9dabd2aa31605d055f69", "guid": "bfdfe7dc352907fc980b868725387e9857e609b7615c935c459fe075cec25b70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4ddcb67d140e9c2dc1186b4294aac8e", "guid": "bfdfe7dc352907fc980b868725387e98cd50060e1a3d88d4e397849658aec08a"}], "guid": "bfdfe7dc352907fc980b868725387e9834253e5bdc0206245482048d9c582479", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982b4218c2c4408513ec7694f52aa96970", "targetReference": "bfdfe7dc352907fc980b868725387e98bb3e3ebadbb0b9a8a4f20f605e3cb3cb"}], "guid": "bfdfe7dc352907fc980b868725387e9822a9b081646adc497104aa8d485235e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98bb3e3ebadbb0b9a8a4f20f605e3cb3cb", "name": "GoogleDataTransport-GoogleDataTransport_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98c64019424081ed2ed9efdee0281dc680", "name": "GoogleDataTransport.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 1}], "type": "standard"}