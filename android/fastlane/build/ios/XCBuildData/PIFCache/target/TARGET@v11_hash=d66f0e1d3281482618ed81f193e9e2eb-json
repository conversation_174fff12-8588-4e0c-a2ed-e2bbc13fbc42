{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c5bd1ab702be031f4f8ae5a0a265e06", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9866e8dd72023e3514edbcb296b7414ba6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c5bd1ab702be031f4f8ae5a0a265e06", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f2c6a0ea92ff0ea56fd314a186fe0e79", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c5bd1ab702be031f4f8ae5a0a265e06", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9856823b89223a87b3afb9d74d1db717dc", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c5bd1ab702be031f4f8ae5a0a265e06", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b21c3a717a162ad336c29b447812b5f0", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804f6baf78139b635a9465c31e188cf0f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bcbaa5c9511f8a267372c0949f9e140d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804f6baf78139b635a9465c31e188cf0f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f4bb9db4f83954f817c3ace50baecbb2", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804f6baf78139b635a9465c31e188cf0f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98159308a9f8ff9a6a2c4a8978e59a5c67", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804f6baf78139b635a9465c31e188cf0f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987f40f058be249c5e109282e9288b5bf2", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804f6baf78139b635a9465c31e188cf0f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f6651d361fa92aefbd1d0c162147577e", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804f6baf78139b635a9465c31e188cf0f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986b460f8c6644357d43e7b7bf57100d01", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804f6baf78139b635a9465c31e188cf0f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9822e4eae656a47403d956199815896089", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9804f6baf78139b635a9465c31e188cf0f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.29.3/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98355c01b416535fe4a9def8b517f4250e", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ece51c779bd4fed1045182bbcb52fe51", "guid": "bfdfe7dc352907fc980b868725387e984785c07fca0456eb09891ce721149caf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e854ecbcdeee5f3b4cfba3d858f44f1", "guid": "bfdfe7dc352907fc980b868725387e986e72ce9f9ad2f466c8122680415575a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9df93259f54521aa3f870c9293d0f39", "guid": "bfdfe7dc352907fc980b868725387e98b3be0a9e1893ec839e42124c94b3d3f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b7303b31ef9244a09dc994d1d1108e", "guid": "bfdfe7dc352907fc980b868725387e988d22d95490a474018d35d331f9c19195", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb3e0514c85292352da9681e1ba06359", "guid": "bfdfe7dc352907fc980b868725387e98a79b9e082128b774912b9a184fa1aabb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824ee4d99bec42ccadf933f959dba99ed", "guid": "bfdfe7dc352907fc980b868725387e989132d5f089c90e0dffd846e63629649f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f051d7ddb7ea00473e6e7fec7f79e08d", "guid": "bfdfe7dc352907fc980b868725387e98871cbd0419f9ca2e2b02b5e913e51064", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae791b6160692bf014a5ea2f945fa9f7", "guid": "bfdfe7dc352907fc980b868725387e98de1f6ca64bfdc0c85686c16abdd31abe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b3d8d09c5a07fdbb8c1329ae8720509", "guid": "bfdfe7dc352907fc980b868725387e98f1c1f571a3fba50705617b8ed5ac3da2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839326ead07a0323b8d75e7ae983ecf40", "guid": "bfdfe7dc352907fc980b868725387e9883e6d03f0d0d91f76b75e7853a3da2a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983acee5ab622407c586fb8d001b95d917", "guid": "bfdfe7dc352907fc980b868725387e983c47941eb9b6050bac7307cdc4d66958", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d7579574325273ffea586cffc50574d", "guid": "bfdfe7dc352907fc980b868725387e98ff70a9935ae0c370f385ae526c48e162", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cb7190b8da0cecc55ad98f04c4239f7", "guid": "bfdfe7dc352907fc980b868725387e98ea79ee7acf0040641d259e2d9b1d5744", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b2e845960854447b6b97f4a4aea11c7", "guid": "bfdfe7dc352907fc980b868725387e98237d1457701ca063cd7bf068da838e1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989839599e8da45e9003e52d1881000a88", "guid": "bfdfe7dc352907fc980b868725387e983bae7ff7f1a8158ce5d68317b326e6f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986684584e45ae811f58bcf7f09c1ffa33", "guid": "bfdfe7dc352907fc980b868725387e980ca8225aaf816ea723421f78fe23c7da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808f950d8cd8ae3d9d1c995371e59eed8", "guid": "bfdfe7dc352907fc980b868725387e98f94ec4eaa2b364461b46f0e19cb73f93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faf0f53998a15702a8356328a1c42b57", "guid": "bfdfe7dc352907fc980b868725387e9852f7cd82bf5a6e08ef02429121992fc5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98135c28ddc3e2580640314954b7c163e1", "guid": "bfdfe7dc352907fc980b868725387e98d7d9b54c4322b14a7ad4797202c61348", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f74c32f67803d0ae6d40bf0646e697cd", "guid": "bfdfe7dc352907fc980b868725387e9825c54dd6831380b4926dc05ee74bea7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a366d36d62cb6d18d6c9a953eb3f71d", "guid": "bfdfe7dc352907fc980b868725387e98353ae3d350dc2de71d1df7e537d725af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddbbe6a5200be65080d00b74c36ad484", "guid": "bfdfe7dc352907fc980b868725387e986a6ef84830ebe1842e366262d0407b29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981980b984541cc9e56a13981473f3cc6a", "guid": "bfdfe7dc352907fc980b868725387e98fccee32a3db63c4975c695fc6b023b88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0ff8f12a192f3bdacab0bdae44c17c5", "guid": "bfdfe7dc352907fc980b868725387e9804085b14a0f87a5e7e2df489b7f6890b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98585c652a42c0a187808e1d537d9cb2bc", "guid": "bfdfe7dc352907fc980b868725387e98bc639ab5b2f39fcda840993858e2c884", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daf0d6ce3533233b29617551a79de0c7", "guid": "bfdfe7dc352907fc980b868725387e98134c3b98bb7e5d40865dad02b9203ffe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd7e16e522cd784f73eb7a07f1c9b23f", "guid": "bfdfe7dc352907fc980b868725387e98eeef678728a7f6b85950c10323f2777e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98461960adc0d4ef758591b72d0264eccc", "guid": "bfdfe7dc352907fc980b868725387e98e8f43402a1da1934a30ea14f2d911353", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984e57428664821be1cfb9c95f8968211a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839b8bbb28f215a11f8ce8dcb1d6368b5", "guid": "bfdfe7dc352907fc980b868725387e98eef217559e83787b1942a9ecf3d1afef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e014d86f05eef78fa2eb16c60b10ad36", "guid": "bfdfe7dc352907fc980b868725387e9800805b7d2c3a0d66dc6e72b28eced717"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852c0968a74f47b4c749a383b91e55432", "guid": "bfdfe7dc352907fc980b868725387e9828fc5dfcb072f63d1f5c8600237fa936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c566cb0ec47943a37e2cd085aca8b70a", "guid": "bfdfe7dc352907fc980b868725387e98664f4f7ad9aa4ad5e5ec872a714bf56c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98790ed79a06496d36ad8f0468f0d13590", "guid": "bfdfe7dc352907fc980b868725387e9875564f7bcce15b2d0977fe0e01b8430d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867e9cd43a9e73be19b5c97cea6b3a422", "guid": "bfdfe7dc352907fc980b868725387e9889c56a7357ca76f854f617ce15454f00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ffbb6427e79e5dd0e7bed593b112b3d", "guid": "bfdfe7dc352907fc980b868725387e986dd0c461c9e3158562e0ad0bb77fb6f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98086ee402954b2e9970849543bf4b67d8", "guid": "bfdfe7dc352907fc980b868725387e9844d64498f7895ecb47c2381f0ec56406"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98106aab1a551ee113efaf9a4d387e5163", "guid": "bfdfe7dc352907fc980b868725387e98b04539d2c19487307ebcbc9ee00754a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ff9dd2fb788c29dec53c780e634be48", "guid": "bfdfe7dc352907fc980b868725387e987752d025321b97c972df5fbb8f380d35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98286a2a6462016adc204eb9b15c363d89", "guid": "bfdfe7dc352907fc980b868725387e98c464b57f5f4384c2498fc957bb7645c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98112ccb693d2b0aea9719bb0442483189", "guid": "bfdfe7dc352907fc980b868725387e988c0a8f2eabb79065dcd591457b8fe7f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fab02cbc4143270075e52eec0bcc46d6", "guid": "bfdfe7dc352907fc980b868725387e983531ec91e68c8aa6f3b8ab41c7c2e893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e58e368f395aa7fd3c36a7d4175d665", "guid": "bfdfe7dc352907fc980b868725387e98eca9f32f05447ba28ed50098c8249655"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56f0fa7cc2c50ae4294217789bb5aa1", "guid": "bfdfe7dc352907fc980b868725387e98e2b8c354d321e800eff3ca236c58082a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98007c8b06f4aaca8eab5af42d5f784f69", "guid": "bfdfe7dc352907fc980b868725387e9836375abf0b6ebbef9dcd9cc3e3cc955e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1384962c761c5cbe9a82c42816ba4d2", "guid": "bfdfe7dc352907fc980b868725387e982d94a2901775239a6ba20527c3975db8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671b6506c0243b9897c223da5046ff6c", "guid": "bfdfe7dc352907fc980b868725387e98ca74df2e3bcc7db8f5d2012d0b887996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98791305915db15a2b272b883b48735b65", "guid": "bfdfe7dc352907fc980b868725387e98fdc527cddb9e8b78ff4f567358bf0649"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809cc6ee45bff3d57f9b290bd371bea67", "guid": "bfdfe7dc352907fc980b868725387e9875c62829ea314dc6300f4c2ea1f1e35e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863f89f6ca1b1ef53c77f59d29a36b648", "guid": "bfdfe7dc352907fc980b868725387e9804294b10dabb84984c1501266a2044f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee8c6bfa70be2685da7c597eae4e71d5", "guid": "bfdfe7dc352907fc980b868725387e98ad4f3cfa74209cf209b57cc1b2566b93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984698febf0210fdeab09d1eb4f47bfdf7", "guid": "bfdfe7dc352907fc980b868725387e985e3fec4ec6b080f33ef79417aa6a48ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b9edf872eb73c68a7fc8cf2fa9ce267", "guid": "bfdfe7dc352907fc980b868725387e9811c176043095d924d8ea408f5223c9fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4460c73098b55eaef41c0628e0e46fa", "guid": "bfdfe7dc352907fc980b868725387e9848516fe8a958e1f582b6d523eee976d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4bb2039f5990be99b00b6b3b2fc1258", "guid": "bfdfe7dc352907fc980b868725387e98a149bb0e30982625d509a123751fbcc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b313791a71dccf841465b6d53e81f04", "guid": "bfdfe7dc352907fc980b868725387e9844d6c6a327eabba1c76ed3791b72c94c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829498e9609cfec905d068d71294afe1e", "guid": "bfdfe7dc352907fc980b868725387e98d7b3670b85bb3d1d186cc4f2599906de"}], "guid": "bfdfe7dc352907fc980b868725387e985587112438be95784a63419727129636", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e27d661740f9dabd2aa31605d055f69", "guid": "bfdfe7dc352907fc980b868725387e98bef9b2816c2a3f7a6d8d9db61e0d99c1"}], "guid": "bfdfe7dc352907fc980b868725387e9873fa90b35f3ff4f616fed171756f8f30", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c8f66ed234e850c3acdf0f0f84603c4d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 1}], "type": "standard"}