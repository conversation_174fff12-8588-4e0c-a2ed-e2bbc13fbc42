{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cdc3f76bdb8e22ddec7d63a2b91eb9f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982079019a9c754df50ad3af8833b73ae2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cdc3f76bdb8e22ddec7d63a2b91eb9f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9880d445135a2210138557855f6e77b4b8", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cdc3f76bdb8e22ddec7d63a2b91eb9f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9829e01e3559919280d0d65e00e8e46378", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cdc3f76bdb8e22ddec7d63a2b91eb9f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cda6ec353c1306b41664e4ed0551ea", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98554518cc9a44a58954affced84080c6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a096f7f7d86a20fc6b80964c26d35ae", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98554518cc9a44a58954affced84080c6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db4299e1294b10c6238577b5891b4afd", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98554518cc9a44a58954affced84080c6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985225b93201146ea093cb40aa035075b6", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98554518cc9a44a58954affced84080c6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989d568ea56c488ba59730247cbebd15b6", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98554518cc9a44a58954affced84080c6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982c552eaacaf81a0b6925302ec991ef01", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98554518cc9a44a58954affced84080c6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982ab75212c2df902355a76a8295771afb", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98554518cc9a44a58954affced84080c6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987750c724862bcff54500dd47cbab31e2", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98554518cc9a44a58954affced84080c6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCrashlytics/FirebaseCrashlytics.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCrashlytics", "PRODUCT_NAME": "FirebaseCrashlytics", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ffa276600929f6f177bc90e877697a7b", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983ff1cfa658d2d256fcb136a7085abd39", "guid": "bfdfe7dc352907fc980b868725387e9807d124b1c19346dfe5a463c450f766b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98515336ff2258fddd25f2e65f0f65a691", "guid": "bfdfe7dc352907fc980b868725387e98675a516e5288fe184e6e7c0e27eb6565"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862101634d1cbfdb949e45480f5e7e3d8", "guid": "bfdfe7dc352907fc980b868725387e982a317ccec47b5dee758acced6af75953"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ba0b449dc4d8476634d5c14351ad6b5", "guid": "bfdfe7dc352907fc980b868725387e98ba760cd7afe09ae04d88a7a88c78e7d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844a31387e38b0635cf30ef58365a976e", "guid": "bfdfe7dc352907fc980b868725387e98329af9ddf120afb0befc5be5daf7d8dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c191408464325160a08dfe504946fa7", "guid": "bfdfe7dc352907fc980b868725387e98a82b34a7bf4d34317889664984a324d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851bbdfc40450e77d3d37dffd1b721714", "guid": "bfdfe7dc352907fc980b868725387e98693e6689d5be281d5a1a09ee7f428c98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f2c80f211b0496b305ffb04779fa66a", "guid": "bfdfe7dc352907fc980b868725387e98febfdfab1262e26cd969ccee4fce3ca2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989df77ee18012114938ddf9fc176e7192", "guid": "bfdfe7dc352907fc980b868725387e980c849b151bbf603f2f82c7cbf304536f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6d608df0c6d654b27829b1c1d730ac8", "guid": "bfdfe7dc352907fc980b868725387e981d52ee5f9151d4b2498aa89430a39e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dda6dd557ddab503e53dca22889b5ba", "guid": "bfdfe7dc352907fc980b868725387e984b7c96e0a832ff8f88e404329e84bd84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98893102eebcdd5e77e6b960ead51a7918", "guid": "bfdfe7dc352907fc980b868725387e98e481ce32f2faf61cc622eb95fc98e90f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988328b1d1bad052c8096b0e248de21b0c", "guid": "bfdfe7dc352907fc980b868725387e9890c11566b04be9eb4d6ff61537c64a10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5dc88c827d4dc30761e849a187e6c49", "guid": "bfdfe7dc352907fc980b868725387e9893cbe33996c6333bdbc5cc70892dd5f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe36cf813fdf4bb4b1ec11869111641e", "guid": "bfdfe7dc352907fc980b868725387e9811b044086dc8798add5adceccf4b4865"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c2404c19dae065eb23b124fac28c3dc", "guid": "bfdfe7dc352907fc980b868725387e98072ce1fb08e7aa34ec634696443fd4f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825cc4cfdaf8606b57b0f30fa67ecce57", "guid": "bfdfe7dc352907fc980b868725387e98e45c0d9ec33b76204e515fb578077fd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98befd168ba5e244610eccc4275f5d3361", "guid": "bfdfe7dc352907fc980b868725387e98910e9056c2b402d6285fde763d1319fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985048ca0b5a729253480837aecae96879", "guid": "bfdfe7dc352907fc980b868725387e98c6905928e36296c6d51ad81dd30eedf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e494bed65ec65e5fffbd928450af85dd", "guid": "bfdfe7dc352907fc980b868725387e9831dc5ee923aca2616d903c2cc134b340"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820b2f4575c2826154d071cc302285c53", "guid": "bfdfe7dc352907fc980b868725387e985376e78e0f6df6f8ad9fc16f99b9a12a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b877b7e32b8c3850c5ee9a3cb099cbb1", "guid": "bfdfe7dc352907fc980b868725387e98a16ab8c11d111ba36eb04c4ab19113ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7c5e69eafc241d1e5747c7ae78ead5f", "guid": "bfdfe7dc352907fc980b868725387e98166b6bd8c318fb2b109b0e80755ba4d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e6b8d4ece839f169abfad9ae9b5d3d4", "guid": "bfdfe7dc352907fc980b868725387e98655f5266f177ab4bf4dc06af26d76847"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fce3d38e1d38ae1f620123df9b7383ac", "guid": "bfdfe7dc352907fc980b868725387e989d865331067715f7b2eee00c1d1baa17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8b538f6fb444e69f6d228123f7e52f", "guid": "bfdfe7dc352907fc980b868725387e98a2a94c93db744f0289859a1e293b442c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6b8846a4b375818ddd7146380fe55be", "guid": "bfdfe7dc352907fc980b868725387e983eb04e674d16914318716509c4cf234b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e67b08897aa61acbd152cc9edf157a4", "guid": "bfdfe7dc352907fc980b868725387e9804e2d22327c4ee6350817214db32d202"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c10fa8044a9925a3f047147a7742a1bc", "guid": "bfdfe7dc352907fc980b868725387e9830c9b5dd62d10e4d4a522a86c2b91227"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815683d023e5f1ac42bf5e72c5ed4f475", "guid": "bfdfe7dc352907fc980b868725387e9834a44f270aa155a06718c94c8a9c3196"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2f78ad86d0f471786f7154b1fcdd930", "guid": "bfdfe7dc352907fc980b868725387e98133cc1a13dbcbb048e9f6b9e619d60a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818bf8bdb5cf8dbe4a8a49d63f333d1b3", "guid": "bfdfe7dc352907fc980b868725387e98553711b3d4efdf4aca079fd7957867b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0043e9d9e01546bb8fdbb24fc0c28ac", "guid": "bfdfe7dc352907fc980b868725387e984f182a49bb813e28b74ec9500bf939fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803b00f6c25fba99382397d8e3c19c0e1", "guid": "bfdfe7dc352907fc980b868725387e984ef96be9dd484918f6144b14789e8279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c71775b0933d5abbdb46d4f1ed9a3623", "guid": "bfdfe7dc352907fc980b868725387e98d10b9b64f78c453b732a3010765027e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b186670d13e189c89eb2ac08700a44d", "guid": "bfdfe7dc352907fc980b868725387e9836d4a92e74ce96fbe101490315ddc829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854325797ca39725db3389391f5c7b140", "guid": "bfdfe7dc352907fc980b868725387e983c8c2f3b963f21fd70a5c39b05925a33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3b474467b88bfe2c53b1ff4479ea3cd", "guid": "bfdfe7dc352907fc980b868725387e98e39bbf01f42a65fe2859d417d78199b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd347455035b11909138af778994d9f1", "guid": "bfdfe7dc352907fc980b868725387e98db0254289c07626070362b21eee66a75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a65e5ba464d940549d6a012419b9fe04", "guid": "bfdfe7dc352907fc980b868725387e9848a83f8fcc0ca1db05c03d61d23420cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa09951db3c1b9a21452dd2c9c42455b", "guid": "bfdfe7dc352907fc980b868725387e98b698a1a4dd58d859e529d50d740dc403"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c82f0c73fba15cf4c976e8249e427131", "guid": "bfdfe7dc352907fc980b868725387e98fa00f8fe1f1e6639fcb861d67e84a4b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b588fc05867c5957009e91486bc97212", "guid": "bfdfe7dc352907fc980b868725387e982c40f7c11efd50cfa755ea899c5430c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98979db7f181163aca98872c7b7297b4cd", "guid": "bfdfe7dc352907fc980b868725387e98a91d400870aec98d9a385d13da4fdb5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889983cd00bc8d99e501d5ab3313d6679", "guid": "bfdfe7dc352907fc980b868725387e98b35ff9b8bdc7ee36edb41cf9cafc1105"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6766c4e77883a99ea42eb00ad76b83d", "guid": "bfdfe7dc352907fc980b868725387e98a0a30fee4eb04513dbe33e94b1727f31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fdd4bc90dc60436ea585e30135d086b", "guid": "bfdfe7dc352907fc980b868725387e98250e486a69cc3fa08bb05827c975444a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab616a90c8e14e2514966a216e849b0f", "guid": "bfdfe7dc352907fc980b868725387e98b20c06b09818c743a635d201df91d9c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db72d2c9385e40c84ec59840f5de0007", "guid": "bfdfe7dc352907fc980b868725387e9841f30a0df34518a7e82f294f32cffdc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5c0fbab4d47c3be4778e1f27eb7952f", "guid": "bfdfe7dc352907fc980b868725387e98e32204efc56a018dcc075e41af23b791"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bfd6cad6f42a5ff33daeeee339ee03b", "guid": "bfdfe7dc352907fc980b868725387e9877a3614f62143999f46d5af971737e4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fa30b5515dd8b6a8340442a4ff88bfd", "guid": "bfdfe7dc352907fc980b868725387e986350bccfaccee3fb8588fcf7ceaed3e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b5b3271fd9899212223842e8455c253", "guid": "bfdfe7dc352907fc980b868725387e987033d9d2bfc8f732fa309b652381b58d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874a7f72148408fec4f32e8a8cdfcb01a", "guid": "bfdfe7dc352907fc980b868725387e9889da627f9d5538ad3b603f37126ae958"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e39957f12bcf9e48c8c5ca2ef44cf5d", "guid": "bfdfe7dc352907fc980b868725387e980dbb5ab1de6871218cb87c265f447838"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877c283924f9bde2bcc3452e3ffa3ec1d", "guid": "bfdfe7dc352907fc980b868725387e984045e0d7d453e680bd8c5e8625c12b32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840918d936324d42414752743485d1b56", "guid": "bfdfe7dc352907fc980b868725387e9881c64716dc6e5a24caddcd8ddaacb4ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b8105667c593fa4d960f5d10d16cd9b", "guid": "bfdfe7dc352907fc980b868725387e98e473d376fd0bbbb16c4841a0c98b5396"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba2939d26e9cf7a617dd83d167449fcf", "guid": "bfdfe7dc352907fc980b868725387e9805b7eeea2481cd53b6e2132249ae703b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d49fa4d041c75799b7daec99d12d005", "guid": "bfdfe7dc352907fc980b868725387e98dc8ca4ecd4092a7e13e737fd2211a3cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858377af97615d158d6df79aa857386a7", "guid": "bfdfe7dc352907fc980b868725387e9880321a468af0e309da05b23659837dd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868c46cef1a41487d3bb2356aa24d8556", "guid": "bfdfe7dc352907fc980b868725387e987f0a5e9de3565867c86972b71a56bb1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d815922d85625ef3db81dab1b37dd946", "guid": "bfdfe7dc352907fc980b868725387e98be532e725287b85b9e859b811e2972fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706ef553b5afe21b219f12ceef5e466a", "guid": "bfdfe7dc352907fc980b868725387e98601455d2c44fdbe99885dc2d7b32085b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac21a7dc9060f2e464bae992b7861965", "guid": "bfdfe7dc352907fc980b868725387e987739930c86d86561634bca1717b65243"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840075a6d9b7346538ba630b08d1c64af", "guid": "bfdfe7dc352907fc980b868725387e98b499916a5101bcf98d288011996e8909"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98020a664dbf520969e2e361a86c3e2c69", "guid": "bfdfe7dc352907fc980b868725387e98d96a4164116df59d29e346c3876e0e23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987869e8f09091febdc68042c0d3d27df9", "guid": "bfdfe7dc352907fc980b868725387e98e90773d0d9eda2592a03a9da100bf11d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842acff6d060bfdee8cf4bf27c43c1c99", "guid": "bfdfe7dc352907fc980b868725387e9849570ca0150fc6df47cfb9e2798cc935"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fbd2ddbe5a50ccae3412f2997478a7e", "guid": "bfdfe7dc352907fc980b868725387e98a7e621cd7682e556c7912401e7285657"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982276ba3b3901e038abd83dda7de7ff90", "guid": "bfdfe7dc352907fc980b868725387e98c232e133a98ac3678990bea648a03ef6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98313faee3f6b9c9a2f8995f3166ceb1aa", "guid": "bfdfe7dc352907fc980b868725387e98f452b5a64b7c1ecb4a6d884dd584ed4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98978f29303474d97fc086a7fb3d98ffec", "guid": "bfdfe7dc352907fc980b868725387e98ce630e5cb5f247c53f255746355e8327"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ff06a51ef098032095d67286ee9e71f", "guid": "bfdfe7dc352907fc980b868725387e9823cd76470f92501f889d356029acf38c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847aa5e8fd7d9daa7bf6f309b7333587e", "guid": "bfdfe7dc352907fc980b868725387e98526f4c41a8cf8a601532f50eed915d7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee214aae8cbeb6957968743f6d9d56a", "guid": "bfdfe7dc352907fc980b868725387e98b8bfc940a952455caefe420f121680b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9275164625dd17f91d7801293a1a6ff", "guid": "bfdfe7dc352907fc980b868725387e985fd9081b9d5b13e3f52de81659173d3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838aa6fa874308c39abb5315a7e461f63", "guid": "bfdfe7dc352907fc980b868725387e98fbb255ba5d30fa887bece8612e35b4bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c27cab5b9dffbc03bc6be17c28eeb79", "guid": "bfdfe7dc352907fc980b868725387e98606cd0f1388052da2f6a37d93a14af96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d02aa41d2c288fac699e91c8941df3c", "guid": "bfdfe7dc352907fc980b868725387e982accffd3ba5fb2af55d451214af507c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b396223b53b57b28654e77871fa886", "guid": "bfdfe7dc352907fc980b868725387e989e3fd8b5b4f18c5025acbfcaabe9f344"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c5218f4bbc59537e292953bbced47b6", "guid": "bfdfe7dc352907fc980b868725387e9879f489b3072b517050e0270e568379b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d38c0702cf3d36ef4a18594a7eed9638", "guid": "bfdfe7dc352907fc980b868725387e98d7cfc0a4f49825e8fc476632b1c965e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c14ced089695df4a3a4dd30493719803", "guid": "bfdfe7dc352907fc980b868725387e98fd0a8c2ae70f2ac8dcabaf8de5092d9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804729cc16d414086f1da36a06f5a9bd3", "guid": "bfdfe7dc352907fc980b868725387e98dcbbca253959b91292a9cd8e80d78db9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98913e461de22d6f9c3b4897f6da277553", "guid": "bfdfe7dc352907fc980b868725387e98e25617fd578240fbc698be27045a2179"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8fd53f5b1de939a6cf3a6d90ad5b4ec", "guid": "bfdfe7dc352907fc980b868725387e9864935117b5693afebb0e7ce9a18127bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243ab7060338e476d42a00d1ac7b0a59", "guid": "bfdfe7dc352907fc980b868725387e98e76225177e558e041f29cddcd47d2981"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca6e988933a2baa70cbff0589448b9de", "guid": "bfdfe7dc352907fc980b868725387e98772c1404400f7b62cc8e11a7b13e7a7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a67671bb965dc5076e0ced94fbdb5b15", "guid": "bfdfe7dc352907fc980b868725387e982160c3ded9908c0566f14aaf7870d1fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834608ca589c0fdc7cd149e6871ea473a", "guid": "bfdfe7dc352907fc980b868725387e98f28c4c05a1b8ee4286773cdda0affcc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852a969206d2fbaed61ea881d5e91ac88", "guid": "bfdfe7dc352907fc980b868725387e98700462a4f08dd252a5d96f764a28bda1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884c17458ed88d743c88736c25d7f92a6", "guid": "bfdfe7dc352907fc980b868725387e98e365ab3d6cf904543ad4418453df35fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c0d0c7745cbbd57cf57f146dcaa71e5", "guid": "bfdfe7dc352907fc980b868725387e9844701a2c680701231361d2f0b34e803d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98857acbe0762b436bc8180506362f72d1", "guid": "bfdfe7dc352907fc980b868725387e9833fc247584bb982a0569fb91f7727b93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3589f7dfa71be43deae528d536dd69e", "guid": "bfdfe7dc352907fc980b868725387e98f7cc7cd491dbc9490ce047f0017fea98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981643426d36080bea5347820fe1f26aea", "guid": "bfdfe7dc352907fc980b868725387e98410f34f7ddf10ef66581ab11539cb25f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881a0814239e4e2a3d86a11fbb1481934", "guid": "bfdfe7dc352907fc980b868725387e98b4c3fe44cdfaa6dc784d616b86880250", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5aac2ea8178b8d653e0f17f3bc9f39e", "guid": "bfdfe7dc352907fc980b868725387e980ed7b70187e9e4bc8ff11f69bfd91c11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f73fa9aaddcf221daf6ac62434152dd7", "guid": "bfdfe7dc352907fc980b868725387e984ff4b76c7f79e010276a786a5a353c22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865f7c1ae53e2cba0b213c64f51b3a6fc", "guid": "bfdfe7dc352907fc980b868725387e98e931b0f13bd26d166bc77c2026b63d5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3cfb172d29587b4fa333f5f95b03668", "guid": "bfdfe7dc352907fc980b868725387e98b71754ff33ba0f63a947b1f3102fed8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c0a0b2f7902aadaca6d89a9cd0c41dc", "guid": "bfdfe7dc352907fc980b868725387e9894f8e604baf38ba8ec46445ad27e8937", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d959cce935f2b132273da0aa02ff5af", "guid": "bfdfe7dc352907fc980b868725387e987db60771d97aef1af3087cd456dd37d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf51b0ecc93b4dad368a31b0fdb9b9f9", "guid": "bfdfe7dc352907fc980b868725387e98c47a8f8487f83a7b17a49a06ffe62d44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea9721440cbab12db153a920750ff429", "guid": "bfdfe7dc352907fc980b868725387e98669a39d454477335cd9d15a85777a7be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98155113d359aeeeb29e367128037eac71", "guid": "bfdfe7dc352907fc980b868725387e983d255d16d227d2fb1a0f2a8a2beb7d4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bdfcb12833c77ccadd7f2c70ed2d12e", "guid": "bfdfe7dc352907fc980b868725387e9884b474f9bbe5af44f565824853cdcc8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feebbad60e3f635e3097cf66ad085c9e", "guid": "bfdfe7dc352907fc980b868725387e98b2bb0ddf41d6ff8ea96a51ac7eb9052f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874389bee7e4bcfef2194b44e9f2b5e3e", "guid": "bfdfe7dc352907fc980b868725387e98724d8a99b2760eafcc5edc74101e3432"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d35a995b3e33607674811668d976e3c", "guid": "bfdfe7dc352907fc980b868725387e9895346edffdeedf13d7834a7d45873f40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaf0c784d6165711f24600439b3a7dc3", "guid": "bfdfe7dc352907fc980b868725387e982451a1daef44170db4c1557ab0d636c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b4122c4b1fe8732d6a3091a3c04e2d9", "guid": "bfdfe7dc352907fc980b868725387e9801fc81a2cddfe2c3f85f280a62650427"}], "guid": "bfdfe7dc352907fc980b868725387e98764e539ed66531c4530f93cd06f2dc80", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981bab0a57ad03e88472eda698460b6598", "guid": "bfdfe7dc352907fc980b868725387e98b60fd06586dbae4c7c817657b13d0a92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c61f81e8a0b95b6e30ea560674baa7c", "guid": "bfdfe7dc352907fc980b868725387e980b7e1bd22e6a51f4a3d3e7f836f1780c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d058e969a33460c1bc4a1f499fd5a53", "guid": "bfdfe7dc352907fc980b868725387e98b16c429f4790a1a2234fcb8c89a2f8d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98decd8ee360c38e6a6b97204917c29861", "guid": "bfdfe7dc352907fc980b868725387e98e6625c23aec5c0f682fb19586650bd7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8be7ecc81d776c6f9c938435c0cedb0", "guid": "bfdfe7dc352907fc980b868725387e98f2d4e8c89b32ce87131bda5f88942959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4025cf30cba8033c9a9f8e254165397", "guid": "bfdfe7dc352907fc980b868725387e9823cf91eaf52db3b51b6a09b86de46c39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827b8bc5e1797cfad3e2985ee63ea2f07", "guid": "bfdfe7dc352907fc980b868725387e982e38a294ba5dbe67c1c8a5666fbeaf94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98394012c4590b4f11d0b02a81715f841e", "guid": "bfdfe7dc352907fc980b868725387e986caac315df0257d8e583abffd2d630db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da159fdb381025b6ca475758460d53e", "guid": "bfdfe7dc352907fc980b868725387e98c9361bb0228f233837ab69105a7c22e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de51689db505db62a286b7999582311d", "guid": "bfdfe7dc352907fc980b868725387e98d7805e53fcfa9237204097f248fb9b04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884cd65abeed13815a20081025a997040", "guid": "bfdfe7dc352907fc980b868725387e98c34a512e904795c0ffcba61979a6bb2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ac88b293eb1ad762c0b3bfe52f8c64e", "guid": "bfdfe7dc352907fc980b868725387e989c67760880108f6cf97e05995a87887e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf18e51fd668df5b44dec34d1d21bac", "guid": "bfdfe7dc352907fc980b868725387e98e780949acce68246fb0d979e4fac0877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98636cc5a6ef981db7fa4d5cb8d407053a", "guid": "bfdfe7dc352907fc980b868725387e9814fc0dbf4f2c569865c21423734f071b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd36967d3b0a0364ec60e770b0ff5ffa", "guid": "bfdfe7dc352907fc980b868725387e98360c21d08a2158cea2e35cbd3aff5f99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d72fd1dda17b385adb7ab327d387dd7c", "guid": "bfdfe7dc352907fc980b868725387e98204d40a66d682a03e91943e8fa212b99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e902562fc5bc6c5d5e3218b141b920c7", "guid": "bfdfe7dc352907fc980b868725387e982c0606333bf699411b88a4c5d157a927"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c2010a01d9c9e714f7a0b5356dab62c", "guid": "bfdfe7dc352907fc980b868725387e9803807da93032bc104d63980a287d1a29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980481ea31a0e27aaf632c8f4dd7a44f2e", "guid": "bfdfe7dc352907fc980b868725387e9888bbd8c26c815ba6f62a4d514561bca1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a07ae3f2d165f443d51b49572b50a6a", "guid": "bfdfe7dc352907fc980b868725387e98f4e0ebd17e788551b93ea3014d744639"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98674bc9a791bd7f127e7b5a3d4679f9cf", "guid": "bfdfe7dc352907fc980b868725387e98a01870dd86b6173550858a33646da591"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a5fddf066bd38590186dbc69f72821e", "guid": "bfdfe7dc352907fc980b868725387e988ec977feeebd3e15528453b9da7c59fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98806a7c9ed4a0d47a17746b8da2f69d30", "guid": "bfdfe7dc352907fc980b868725387e98a98950d298ac6f26dfd5df39c494e709"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8d3cc8bfbfdea7e5e24016573f1f7db", "guid": "bfdfe7dc352907fc980b868725387e98d78b5640a3c3f7207d3d15ef57faa74a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802ffa4db2219717b65856c4ceecc203d", "guid": "bfdfe7dc352907fc980b868725387e9820eaa2092fc208f52a6a7580196d90d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca3c2b579840da8d015c3d0fa3610ff3", "guid": "bfdfe7dc352907fc980b868725387e987b3086c3ef7342d1e64e7b2c520c63c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9ffe94385ceda0d07258120f445738a", "guid": "bfdfe7dc352907fc980b868725387e9858b6d6de4c6e55105822af61070a8cba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98206da4e9153880b0b481c61e81392cb0", "guid": "bfdfe7dc352907fc980b868725387e984eb3effe9ad8a3d2825221643c3e0641"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bef2d3cbec059df251f8353408950c9f", "guid": "bfdfe7dc352907fc980b868725387e98f27e5c0c8883e54a018c0fd061564dc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d78f396d9b508c4b229caccf070da48", "guid": "bfdfe7dc352907fc980b868725387e98b9ecba441cb230640bce6f536e4e0b45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986adbde9185fa2f1e02a9a32d1e3e3c98", "guid": "bfdfe7dc352907fc980b868725387e985ecdbf9ab9bce20a1c72e8acd1d1c0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7b83948a4c46a7129b61a176c18472c", "guid": "bfdfe7dc352907fc980b868725387e98a3c8a8e6221d672f6be72dd658d88034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b95c6163e25b94f77e1970e4e9fe662", "guid": "bfdfe7dc352907fc980b868725387e98389450c46fa1efb68c761967402e2840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98344b4511f7c29916dc3ad42fe77014f9", "guid": "bfdfe7dc352907fc980b868725387e988807ead30ab111f1b4e2b7bb3fd10954"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c41240a32672a7a62a7d96ef43562fce", "guid": "bfdfe7dc352907fc980b868725387e982718deac78638d8d23161abf86cfc068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988caa8b13421f00fc4c6b2d1767a7e8de", "guid": "bfdfe7dc352907fc980b868725387e98a2a70e5249df918d12ad8ed845e034a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d33883e8f6f06d775f6a4332260915", "guid": "bfdfe7dc352907fc980b868725387e98e91f34e7641dbcf20475ce21bc6fa88b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887699129b3f790519dd092f8d6b92d8d", "guid": "bfdfe7dc352907fc980b868725387e98213256fefe3a6fa76e91dadbcd682489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aed89051b3f2340fccd7e18a89900bd6", "guid": "bfdfe7dc352907fc980b868725387e9804cb874f1a23be125654ba33e10896f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988746a2dd87c96e738dd78fca6702e5a8", "guid": "bfdfe7dc352907fc980b868725387e98870ea1cdbd06679ccd67650ad715e994"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3488d85d5523b7afbc409dcdde2b250", "guid": "bfdfe7dc352907fc980b868725387e9811c934f4d61afb650996fb03fba07576"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e1d003e4083d22d636747feb48a3a41", "guid": "bfdfe7dc352907fc980b868725387e9847accf0edbff82307d463bad4365ef27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855eb14d802a73da2a683450d203ec955", "guid": "bfdfe7dc352907fc980b868725387e98b80d5eb3d46d4da1a453cea4eea6ab3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980600548af3fdb42ecadf9e9849a8274a", "guid": "bfdfe7dc352907fc980b868725387e989f02edf8ee419391d5c51a050bb1a5c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d11da5be0892a0e609e3b6a96cf13f", "guid": "bfdfe7dc352907fc980b868725387e98c8ac603e2c047b9944edec0eb2e8847e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825b3380c63555f252d32dae6f6e6139b", "guid": "bfdfe7dc352907fc980b868725387e987ebee3b34587ea9826a747edeabac916"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a497f26f94dbb36429e6da5b347ad658", "guid": "bfdfe7dc352907fc980b868725387e98379098211f06e47f9b4188ba22aba6a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885825c6b2cba748d20912c268b745f5c", "guid": "bfdfe7dc352907fc980b868725387e98df9a774029f431e00efbd91edc3082c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98459a92c0b899ca32168fa4f671c0069c", "guid": "bfdfe7dc352907fc980b868725387e98ce6c2ec2a9cd84d49f9255527bec240c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c80b70f2f5e3aa4884e56f0913886c60", "guid": "bfdfe7dc352907fc980b868725387e98c688f2cf88d78c52ef978464d23ac89d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ea0401796f130aac2643ae70c544116", "guid": "bfdfe7dc352907fc980b868725387e98290c0dab10c4703c576812fefe52bb48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98124b3d8e243e0a9ce8719ac983509564", "guid": "bfdfe7dc352907fc980b868725387e98b6a0520b7afe094a86b226ae78700ab8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053e4138e24279c36ac0a6df06d46471", "guid": "bfdfe7dc352907fc980b868725387e980e2ec42e1266296b15e343610489e4a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805f81dd1ce37291c2076fd94a87a951d", "guid": "bfdfe7dc352907fc980b868725387e9840e8c4123d1e9da21aba9b24151bda88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847b83d48b6dbd034e16c5b6494af1f76", "guid": "bfdfe7dc352907fc980b868725387e98eef413d9c5d945cbad649ff347be079a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98111ffa8624635c4d774407543e740f14", "guid": "bfdfe7dc352907fc980b868725387e9831da6b5f473fa92618cadb4a6904c94a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985383cffe118819c0b72c1e1e139ba227", "guid": "bfdfe7dc352907fc980b868725387e98660e52f79929cb646b55e78a48c509c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a4f15c123f9faeaa4e3e7e3bc1a4286", "guid": "bfdfe7dc352907fc980b868725387e981b59521eb69b35a0bfd5ec0ccaa2bf66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d92944f14d823b2e0c2202dd2da90678", "guid": "bfdfe7dc352907fc980b868725387e98b306ee48039d6ff698ae577e830c670a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986170843bd92af32b158b2732ea1a1c5b", "guid": "bfdfe7dc352907fc980b868725387e9857be0dc601144ab90172ea6e9973aba6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b60b4b3056c48b1b91874261b656462", "guid": "bfdfe7dc352907fc980b868725387e98fa8e875f2d98da24096d4c9375090ffc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980815b32a24ff4876bee08ae78c69c955", "guid": "bfdfe7dc352907fc980b868725387e98824394fb566c32c0ce95c59b480b7744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e78db66c5a82e05b1088a62b60e51c6e", "guid": "bfdfe7dc352907fc980b868725387e98b26af66163bf5dbbb4d3fbcc4b234e34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ce04f33598d8e23ed47f66b0dbd8b6a", "guid": "bfdfe7dc352907fc980b868725387e98ea6bbd99ac99f9a7f748a03696a5f59b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c2ddcca128b4cb6bfce27bba732ffae", "guid": "bfdfe7dc352907fc980b868725387e98ecbbd0617c64a4a7c2732358c3f58780"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbf0a19ef12d56e275a6fcc6a1a4af7f", "guid": "bfdfe7dc352907fc980b868725387e98c1edac7c1d0a57e10d69edf478f7343b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854945606fcb4cbb3e096c64c4cb7a752", "guid": "bfdfe7dc352907fc980b868725387e98d9efeba4a7008500430f7d1bb90e0572"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d995936b89ea3fdc2367abc66aa0530", "guid": "bfdfe7dc352907fc980b868725387e985d21206bfe4e98535b158b6d747f97a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e263fa3dd8888c12f50f11c8a2dfa22c", "guid": "bfdfe7dc352907fc980b868725387e98109e7919d24be1fb9eddb0bf783f5e2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810b24b4760e2aa15d7665d21b28e351b", "guid": "bfdfe7dc352907fc980b868725387e9806c1c094dd7dc32cfc2a29af8294f003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cfacfffb4a3d49bc7270fb210e62d2b", "guid": "bfdfe7dc352907fc980b868725387e983ccfba943083d7a2c7c3916e60f267c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4d73051a3a1a37ea28be9e00b75113c", "guid": "bfdfe7dc352907fc980b868725387e9882ec84d464aa3719978dd8202f47444e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f94415b5858fbe41c5eed80055722b5c", "guid": "bfdfe7dc352907fc980b868725387e98d733edb265d2a1c1f696d4d67d17943f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98716991ab7677512e82db2691b336fc77", "guid": "bfdfe7dc352907fc980b868725387e98bcdb5b212a0e678910af1b85771c80ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801705d4d52477f60bf42cae2ecdf733e", "guid": "bfdfe7dc352907fc980b868725387e98016897fe500593f1029b854a8a5a7346"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd4e596a2a3353cb3f9f6eb7fa9e9d0", "guid": "bfdfe7dc352907fc980b868725387e988c89585cf8c84de8eb808b6f9e91e25c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892b6a8b6e6f2c8fde22cfc3aa80bf44c", "guid": "bfdfe7dc352907fc980b868725387e98f5071cd1aa7ea76fa7c1bf0049ff78bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886aa1d06121bbfadd30845294aeae625", "guid": "bfdfe7dc352907fc980b868725387e9880e867846c4ba77a7f5eebe207c6e014"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980593f1f8e4caabd14105ab3dde99d62e", "guid": "bfdfe7dc352907fc980b868725387e98f40b56aef629a0d7be279cfcfbc0c58b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce5122e3ffc101e1876fb67de4e8e2a0", "guid": "bfdfe7dc352907fc980b868725387e98ae5c57c29f7b1721dec5a01988e51a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ae15cdb15a39b379212d1d2ab861a38", "guid": "bfdfe7dc352907fc980b868725387e9865c99620f02baf3d4387fbd7fc5e908a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e46d20d7d1a393c4dfc53aea02696acd", "guid": "bfdfe7dc352907fc980b868725387e983dc77951cb0fd252f4ddfaad0ebce100"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98821faf777d7ca58e7a716c8e5db809ad", "guid": "bfdfe7dc352907fc980b868725387e98e06b727d66047270bf3a53d0287e409a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a1b6b165a06b1d1aa1f7bdb66d3d2c", "guid": "bfdfe7dc352907fc980b868725387e98e029ab38688e747df6ca2b36d1c099d5"}], "guid": "bfdfe7dc352907fc980b868725387e98ed1a149c1bd6f3d2cf09e6d8178c10f0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e27d661740f9dabd2aa31605d055f69", "guid": "bfdfe7dc352907fc980b868725387e9807576ce3dfaa11ad67e9bcb7206e6978"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807e734ef3e50e9360b339f4a261e0219", "guid": "bfdfe7dc352907fc980b868725387e98ab60a675312e7421b61fd56e72e53c6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4ddcb67d140e9c2dc1186b4294aac8e", "guid": "bfdfe7dc352907fc980b868725387e98e39954932ba8cdb29df62869bfe41b64"}], "guid": "bfdfe7dc352907fc980b868725387e980d332392ccac75d81d7e036ef9728e59", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b4b3593e9f334ce6d8ab98debd9708bf", "targetReference": "bfdfe7dc352907fc980b868725387e980c5ce7972ce50f1749855d2e6c168f02"}], "guid": "bfdfe7dc352907fc980b868725387e98d469000d4aa8310b69c52ff4458f44b1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e980c5ce7972ce50f1749855d2e6c168f02", "name": "FirebaseCrashlytics-FirebaseCrashlytics_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e986f81f65466c0a2e7395c158e76999d58", "name": "FirebaseCrashlytics", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9895149aed7919b80949f84c354afe15f1", "name": "FirebaseCrashlytics.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 1}], "type": "standard"}