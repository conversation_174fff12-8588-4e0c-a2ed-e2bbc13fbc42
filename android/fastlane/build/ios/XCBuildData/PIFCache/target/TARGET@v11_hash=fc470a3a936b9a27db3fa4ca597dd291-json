{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98717a908f9359e140e09b1b590f13b057", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9859202997ab40ab25bbfc3f2f402899e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98717a908f9359e140e09b1b590f13b057", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e0519a4d2afff0939ba6a33b357d15f", "name": "Debug-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98717a908f9359e140e09b1b590f13b057", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984dd4269081a8c2297e5f4299cbeac827", "name": "Debug-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98717a908f9359e140e09b1b590f13b057", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d021ed499fe0291fe28a6d2abb768b6", "name": "Debug-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985670d5bed3c6e089b10535c3dbddb0ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a10acd6ca4642c2284fe63d1ec3915b5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985670d5bed3c6e089b10535c3dbddb0ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e0d47c5fdac0d9d18ff05a74a70c37eb", "name": "Profile-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985670d5bed3c6e089b10535c3dbddb0ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98466bd8b20aff2e59435fb1e32dd97a34", "name": "Profile-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985670d5bed3c6e089b10535c3dbddb0ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aac1d86d0b1f8260a9f82c25bfcbeed5", "name": "Profile-staging"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985670d5bed3c6e089b10535c3dbddb0ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984bb3a509c3675035c8968f16a47a8af8", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985670d5bed3c6e089b10535c3dbddb0ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ca37eae0ee6931d8abf6c4a4ee4fac0a", "name": "Release-dev"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985670d5bed3c6e089b10535c3dbddb0ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9818b0fdae7882bf8a03fd256becd73457", "name": "Release-prod"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985670d5bed3c6e089b10535c3dbddb0ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f2d9f658da5f72382c6f5d8b7d1f7eb3", "name": "Release-staging"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9895ec3c945766461ad462f05110c4b829", "guid": "bfdfe7dc352907fc980b868725387e98765f216c1dbfb4cf6eb46b4dffc86ab8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980126c19d2a3e16611c0c10fc8be09077", "guid": "bfdfe7dc352907fc980b868725387e984ac464961c33eeba0c7801a0803fd7ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989309020ab1bd31d2cb4c95c0f86a03ef", "guid": "bfdfe7dc352907fc980b868725387e9897a37af855ac9f94f9f6c7d073ebf108"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc966b01b6c3eb013b11bcb3bfe9f954", "guid": "bfdfe7dc352907fc980b868725387e980862a20af75dde750bdfbee52c1ca175", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846cc1c789b7130e3f29b98d330d03a67", "guid": "bfdfe7dc352907fc980b868725387e9874c55b4359807506a225264ef53d4a10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b173dfe6776a8aa881f4cfda24a3619", "guid": "bfdfe7dc352907fc980b868725387e98757b8465d10703395c42af7e0e77cdd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c76357c876609cc855761a166db4c20", "guid": "bfdfe7dc352907fc980b868725387e985316318574539cdd6e775fa75cde2d2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987935f83fd9824c6322c5a049c7d1094b", "guid": "bfdfe7dc352907fc980b868725387e982f81260713acfa69033849cfbffa14df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f42a518a701df03df4868a1e70f015d5", "guid": "bfdfe7dc352907fc980b868725387e9874cd6e6c73376b3e97bfdd24e33001bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98443d1e452dfaed2e8348b3b41a97cdf5", "guid": "bfdfe7dc352907fc980b868725387e98e8a04dcc98db5e6d64e8e42f64c256e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4f71d7f547baa70f6e9c3a18eeed436", "guid": "bfdfe7dc352907fc980b868725387e983b6f241ed2ea68ab3f587d2e99aed11b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864a6f2d903a4c9339a658f4c0224d115", "guid": "bfdfe7dc352907fc980b868725387e986ea2c417602b105ce1dc09c6a9e09b57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4254aed6658e8226c11548197fe19b2", "guid": "bfdfe7dc352907fc980b868725387e985cd1c735100b58828a50f39eb295f2cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a93288c31a4e027e62b7ed4728171a17", "guid": "bfdfe7dc352907fc980b868725387e9824d58dd3eff241afdc50687174b688f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877e490b5560f2246ae04ed9269e13788", "guid": "bfdfe7dc352907fc980b868725387e98ebe627ffdaa0083d5235141286c6e485"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c20bad7859098c72a3e712ac51c1ea5", "guid": "bfdfe7dc352907fc980b868725387e98199e482cc5aa531d2fd9872ae0f0cd4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986911bada854a485bcbce423ffa7731d2", "guid": "bfdfe7dc352907fc980b868725387e985d22d794f3c1ff5313a7d59753451815", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98415ae2fdfb169fb22a45ca65c4f98324", "guid": "bfdfe7dc352907fc980b868725387e98b4996f618e07dcfbd7f0d26b5d1d99d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a288efc6ab4cdddbeaee62014ca5124f", "guid": "bfdfe7dc352907fc980b868725387e987b68c3ac621faaaf2e19b34bf9b039cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985065670a4151a2eb5c0ca1d2c461f6de", "guid": "bfdfe7dc352907fc980b868725387e98a2443ae50463e11bba0c1499b64b6585", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f148abc72c5434f5ebbb065599c58441", "guid": "bfdfe7dc352907fc980b868725387e9878f8f6d5b9dce61c2b840406688337bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bff68e0ffc8887a06355eff36298ac0", "guid": "bfdfe7dc352907fc980b868725387e982dd40f584b90581163a903d6922e4c07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98836c1a611e455ca6d4e11dfc5e08a513", "guid": "bfdfe7dc352907fc980b868725387e986dea1f32a20a841008bdd52b44bb2482"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874804bc3085fcd687c5b47f8a1b62b87", "guid": "bfdfe7dc352907fc980b868725387e982ca3638221b9682c9ff3f49a9aae7867", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984278ba754cfe1f806c6561a88b09e2ab", "guid": "bfdfe7dc352907fc980b868725387e986377c667e0946c35164911359db64772"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b81a25959d9b4945511cfee9df802725", "guid": "bfdfe7dc352907fc980b868725387e983dc09b928340311d47ee4a2a6eb62303", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbeb005badb4340db2707777c0d3a285", "guid": "bfdfe7dc352907fc980b868725387e987cd98595276027480e3858752a0c3c0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985abb0f8e4020620aa2b5c1fbf1368f74", "guid": "bfdfe7dc352907fc980b868725387e98f78f611eafddeb7985fbe028f26bc8c6"}], "guid": "bfdfe7dc352907fc980b868725387e9854a2ddecdc037609c5a5b1007d1922b6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dbad3b91733d9bd140d3c6c3dc95f619", "guid": "bfdfe7dc352907fc980b868725387e981fc2d008d4eae09f235673720608582a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98881a4effde9b3cfff913f0f8d8ea44b4", "guid": "bfdfe7dc352907fc980b868725387e980985e538a15763ae6a89d10e4c65a731"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f3385c83c0ea1a13643f4b7e9eea00c", "guid": "bfdfe7dc352907fc980b868725387e980a2cedf3a53b81d3627779ec5a7dbdf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0804a50338701eb65f530fe83f445ea", "guid": "bfdfe7dc352907fc980b868725387e9818cb3b31ef753f07e45b56e7583a7532"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984110421ab444994c14e43599e218890d", "guid": "bfdfe7dc352907fc980b868725387e989703e55cc3509f45c5f5247feab41316"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822b6361b705ff2cae70bc5d45b6e308c", "guid": "bfdfe7dc352907fc980b868725387e9824bef7937c3badb352d2d34717050401"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98171392ee1d1df1ecde4fc5fdf9d07cad", "guid": "bfdfe7dc352907fc980b868725387e98e95b16aec27f32a672882978a42e7c2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3a7c6ce605b56c5b77f66492859079b", "guid": "bfdfe7dc352907fc980b868725387e989a8930046a92dce783c8cb4dc9307d61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc4c6de72606d3708bebb8e7bcc94e58", "guid": "bfdfe7dc352907fc980b868725387e989efc8db7580963afefc880ed3dc30779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7dfa1f971bb169bc0898e59d6fdce9f", "guid": "bfdfe7dc352907fc980b868725387e983ff309eec58c2043be2ff91ed9ca6744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982214463386d75339c4d3c472ed848a08", "guid": "bfdfe7dc352907fc980b868725387e98e0ef9739c41f5f959c1fcb40c86a8612"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98472dc6b5523e8051824f00c1afa88431", "guid": "bfdfe7dc352907fc980b868725387e98b9f476a24b1bf0d7affedd022a404d6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c907cffe2538b79bdfee9117020b1b58", "guid": "bfdfe7dc352907fc980b868725387e9848c2ee3d542fb869b11d4687b53ae85d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d095b05f7acb569a892795695613a7c9", "guid": "bfdfe7dc352907fc980b868725387e9821e433f864acaba9c9445e5bc4ec169c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848e8f4505d9ecfba010514df1afe4e81", "guid": "bfdfe7dc352907fc980b868725387e98fd9ff80336903c7e9f24e671a70145d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981630bf3743144394a61cecdc7fc022fd", "guid": "bfdfe7dc352907fc980b868725387e98bc20592b8a660b39cc5a9b0333d303cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98271f295e89a2f0a97a54cadcee3b6b23", "guid": "bfdfe7dc352907fc980b868725387e98db20e4cef8a8599b6494203a640a3bd6"}], "guid": "bfdfe7dc352907fc980b868725387e981db7bb360f3d3c6b7beb73dcf7c50197", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e27d661740f9dabd2aa31605d055f69", "guid": "bfdfe7dc352907fc980b868725387e981bd2bbd5b5663f6e238d88a845bfe304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807e734ef3e50e9360b339f4a261e0219", "guid": "bfdfe7dc352907fc980b868725387e981e7ddaa1082875b955386b08d8af9f4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4ddcb67d140e9c2dc1186b4294aac8e", "guid": "bfdfe7dc352907fc980b868725387e98eced205518724e8a6c9d89628ff2c553"}], "guid": "bfdfe7dc352907fc980b868725387e982f5906cbd49232e2a0fc31bbea567577", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98373bc3c8dc89d366ca934471a185662c", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e981351c5b27112c0366f14604b4a3429a9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile-staging", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-dev", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-prod", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-staging", "provisioningStyle": 1}], "type": "standard"}