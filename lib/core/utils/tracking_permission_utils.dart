import 'dart:io';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:flutter_audio_room/core/utils/analytics_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';

/// Utility class for managing iOS App Tracking Transparency permissions
class TrackingPermissionUtils {
  TrackingPermissionUtils._();

  static const String _tag = 'TrackingPermissionUtils';

  /// Request tracking permission for iOS
  /// Returns true if permission is granted or not required (Android)
  static Future<bool> requestTrackingPermission() async {
    if (!Platform.isIOS) {
      // Android doesn't need this permission
      LogUtils.d('Android platform - tracking permission not required', tag: _tag);
      return true;
    }

    try {
      // Check current tracking authorization status
      final status = await AppTrackingTransparency.trackingAuthorizationStatus;
      
      LogUtils.d('Current tracking status: $status', tag: _tag);

      if (status == TrackingStatus.notDetermined) {
        // Request permission
        final requestedStatus = await AppTrackingTransparency.requestTrackingAuthorization();
        LogUtils.d('Requested tracking status: $requestedStatus', tag: _tag);
        
        final isAuthorized = requestedStatus == TrackingStatus.authorized;
        
        // Enable/disable analytics based on permission
        await AnalyticsUtils.setAnalyticsEnabled(isAuthorized);
        
        // Track permission result
        await AnalyticsUtils.trackCustomEvent(
          eventName: 'tracking_permission_requested',
          parameters: {
            'status': requestedStatus.toString(),
            'granted': isAuthorized,
          },
        );
        
        return isAuthorized;
      } else {
        // Permission already determined
        final isAuthorized = status == TrackingStatus.authorized;
        await AnalyticsUtils.setAnalyticsEnabled(isAuthorized);
        
        LogUtils.d('Tracking permission already determined: $isAuthorized', tag: _tag);
        return isAuthorized;
      }
    } catch (e) {
      LogUtils.e('Failed to request tracking permission: $e', tag: _tag);
      // If permission request fails, disable analytics
      await AnalyticsUtils.setAnalyticsEnabled(false);
      return false;
    }
  }

  /// Check if tracking is authorized
  static Future<bool> isTrackingAuthorized() async {
    if (!Platform.isIOS) {
      return true;
    }

    try {
      final status = await AppTrackingTransparency.trackingAuthorizationStatus;
      final isAuthorized = status == TrackingStatus.authorized;
      LogUtils.d('Tracking authorized: $isAuthorized', tag: _tag);
      return isAuthorized;
    } catch (e) {
      LogUtils.e('Failed to check tracking status: $e', tag: _tag);
      return false;
    }
  }

  /// Get current tracking status
  static Future<TrackingStatus> getTrackingStatus() async {
    if (!Platform.isIOS) {
      return TrackingStatus.authorized;
    }

    try {
      final status = await AppTrackingTransparency.trackingAuthorizationStatus;
      LogUtils.d('Current tracking status: $status', tag: _tag);
      return status;
    } catch (e) {
      LogUtils.e('Failed to get tracking status: $e', tag: _tag);
      return TrackingStatus.denied;
    }
  }

  /// Get human-readable status description
  static String getStatusDescription(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.authorized:
        return 'Enabled - Helps improve app experience';
      case TrackingStatus.denied:
        return 'Disabled - Limited analytics';
      case TrackingStatus.restricted:
        return 'Restricted by device settings';
      case TrackingStatus.notDetermined:
        return 'Not set - Tap to configure';
      default:
        return 'Unknown status';
    }
  }

  /// Check if we should show permission request
  /// Returns true if permission is not determined and we haven't asked recently
  static Future<bool> shouldRequestPermission() async {
    if (!Platform.isIOS) {
      return false;
    }

    try {
      final status = await AppTrackingTransparency.trackingAuthorizationStatus;
      return status == TrackingStatus.notDetermined;
    } catch (e) {
      LogUtils.e('Failed to check if should request permission: $e', tag: _tag);
      return false;
    }
  }

  /// Initialize tracking permissions and analytics
  /// Call this during app startup
  static Future<void> initializeTracking() async {
    try {
      LogUtils.d('Initializing tracking permissions...', tag: _tag);
      
      if (!Platform.isIOS) {
        // For Android, just enable analytics
        await AnalyticsUtils.setAnalyticsEnabled(true);
        LogUtils.d('Android - analytics enabled by default', tag: _tag);
        return;
      }

      // For iOS, check current permission status
      final status = await AppTrackingTransparency.trackingAuthorizationStatus;
      final isAuthorized = status == TrackingStatus.authorized;
      
      // Enable analytics based on current permission
      await AnalyticsUtils.setAnalyticsEnabled(isAuthorized);
      
      LogUtils.d('iOS tracking initialized - authorized: $isAuthorized', tag: _tag);
      
      // Track initialization
      await AnalyticsUtils.trackCustomEvent(
        eventName: 'tracking_initialized',
        parameters: {
          'platform': Platform.operatingSystem,
          'status': status.toString(),
          'analytics_enabled': isAuthorized,
        },
      );
      
    } catch (e) {
      LogUtils.e('Failed to initialize tracking: $e', tag: _tag);
      // Fallback: disable analytics if initialization fails
      await AnalyticsUtils.setAnalyticsEnabled(false);
    }
  }

  /// Request permission with delay (recommended for better UX)
  /// Call this after user has used the app for a while
  static Future<bool> requestPermissionWithDelay({
    Duration delay = const Duration(seconds: 3),
  }) async {
    if (!Platform.isIOS) {
      return true;
    }

    try {
      // Check if we should request permission
      final shouldRequest = await shouldRequestPermission();
      if (!shouldRequest) {
        LogUtils.d('Permission already determined, skipping request', tag: _tag);
        return await isTrackingAuthorized();
      }

      // Add delay for better user experience
      await Future.delayed(delay);
      
      LogUtils.d('Requesting tracking permission after delay...', tag: _tag);
      return await requestTrackingPermission();
      
    } catch (e) {
      LogUtils.e('Failed to request permission with delay: $e', tag: _tag);
      return false;
    }
  }

  /// Open iOS Settings app to tracking settings
  /// Use this if user wants to change permission later
  static Future<void> openTrackingSettings() async {
    if (!Platform.isIOS) {
      LogUtils.d('Opening tracking settings not available on Android', tag: _tag);
      return;
    }

    try {
      // This will open iOS Settings app
      // Note: There's no direct way to open tracking settings specifically
      // User will need to navigate to Privacy & Security > Tracking
      await AppTrackingTransparency.requestTrackingAuthorization();
      LogUtils.d('Opened tracking settings', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to open tracking settings: $e', tag: _tag);
    }
  }

  /// Get advertising identifier (IDFA) if available
  /// Returns null if tracking is not authorized
  static Future<String?> getAdvertisingIdentifier() async {
    if (!Platform.isIOS) {
      LogUtils.d('IDFA not available on Android', tag: _tag);
      return null;
    }

    try {
      final isAuthorized = await isTrackingAuthorized();
      if (!isAuthorized) {
        LogUtils.d('Tracking not authorized, IDFA not available', tag: _tag);
        return null;
      }

      final idfa = await AppTrackingTransparency.getAdvertisingIdentifier();
      LogUtils.d('Retrieved IDFA: ${idfa.substring(0, 8)}...', tag: _tag);
      return idfa;
    } catch (e) {
      LogUtils.e('Failed to get advertising identifier: $e', tag: _tag);
      return null;
    }
  }

  /// Track permission change event
  /// Call this when permission status changes
  static Future<void> trackPermissionChange({
    required TrackingStatus oldStatus,
    required TrackingStatus newStatus,
  }) async {
    try {
      await AnalyticsUtils.trackCustomEvent(
        eventName: 'tracking_permission_changed',
        parameters: {
          'old_status': oldStatus.toString(),
          'new_status': newStatus.toString(),
          'analytics_enabled': newStatus == TrackingStatus.authorized,
        },
      );

      LogUtils.d('Tracked permission change: $oldStatus -> $newStatus', tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track permission change: $e', tag: _tag);
    }
  }

  // ==================== Voice Call Tracking Events ====================

  /// Track instant voice call events
  /// Call this to track instant voice call start, end, and duration
  static Future<void> trackInstantVoiceCall({
    required String eventType, // 'call_started', 'call_ended', 'call_failed'
    String? matchId,
    String? peerId,
    Duration? callDuration,
    String?
        callEndReason, // 'user_hangup', 'peer_hangup', 'timeout', 'network_error'
    bool? isEncrypted,
    bool? success,
    String? errorMessage,
    Map<String, Object>? additionalParams,
  }) async {
    try {
      // Track instant voice call events using custom event
      await AnalyticsUtils.trackCustomEvent(
        eventName: 'instant_voice_call_event',
        parameters: {
          'event_type': eventType,
          'call_type': 'instant',
          if (matchId != null) 'match_id': matchId,
          if (peerId != null) 'peer_id': peerId,
          if (callDuration != null)
            'call_duration_seconds': callDuration.inSeconds,
          if (callDuration != null)
            'call_duration_minutes': (callDuration.inSeconds / 60).round(),
          if (callEndReason != null) 'end_reason': callEndReason,
          if (isEncrypted != null) 'is_encrypted': isEncrypted,
          'success': success ?? true,
          if (errorMessage != null) 'error_message': errorMessage,
          if (additionalParams != null) ...additionalParams,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d(
          'Instant voice call event tracked: $eventType${matchId != null ? ' (Match: $matchId)' : ''}',
          tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track instant voice call event: $e', tag: _tag);
    }
  }

  /// Track friend voice call events
  /// Call this to track friend voice call start, end, and duration
  static Future<void> trackFriendVoiceCall({
    required String
        eventType, // 'call_started', 'call_ended', 'call_failed', 'call_answered', 'call_rejected'
    String? callId,
    String? friendId,
    String? friendName,
    Duration? callDuration,
    String?
        callEndReason, // 'user_hangup', 'peer_hangup', 'timeout', 'network_error', 'rejected'
    bool? isEncrypted,
    bool? isCaller,
    bool? success,
    String? errorMessage,
    Map<String, Object>? additionalParams,
  }) async {
    try {
      // Track friend voice call events using custom event
      await AnalyticsUtils.trackCustomEvent(
        eventName: 'friend_voice_call_event',
        parameters: {
          'event_type': eventType,
          'call_type': 'friend',
          if (callId != null) 'call_id': callId,
          if (friendId != null) 'friend_id': friendId,
          if (friendName != null) 'friend_name': friendName,
          if (callDuration != null)
            'call_duration_seconds': callDuration.inSeconds,
          if (callDuration != null)
            'call_duration_minutes': (callDuration.inSeconds / 60).round(),
          if (callEndReason != null) 'end_reason': callEndReason,
          if (isEncrypted != null) 'is_encrypted': isEncrypted,
          if (isCaller != null) 'is_caller': isCaller,
          'success': success ?? true,
          if (errorMessage != null) 'error_message': errorMessage,
          if (additionalParams != null) ...additionalParams,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d(
          'Friend voice call event tracked: $eventType${callId != null ? ' (Call: $callId)' : ''}',
          tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track friend voice call event: $e', tag: _tag);
    }
  }

  /// Track voice call quality metrics
  /// Call this to track call quality and performance metrics
  static Future<void> trackVoiceCallQuality({
    required String callType, // 'instant' or 'friend'
    String? callId,
    String? matchId,
    int? audioQualityScore, // 1-5 rating
    int? connectionQualityScore, // 1-5 rating
    int? latencyMs,
    int? packetLossPercentage,
    String? audioCodec,
    String? networkType, // 'wifi', '4g', '5g', etc.
    bool? hadConnectionIssues,
    Map<String, Object>? additionalMetrics,
  }) async {
    try {
      await AnalyticsUtils.trackCustomEvent(
        eventName: 'voice_call_quality_metrics',
        parameters: {
          'call_type': callType,
          if (callId != null) 'call_id': callId,
          if (matchId != null) 'match_id': matchId,
          if (audioQualityScore != null)
            'audio_quality_score': audioQualityScore,
          if (connectionQualityScore != null)
            'connection_quality_score': connectionQualityScore,
          if (latencyMs != null) 'latency_ms': latencyMs,
          if (packetLossPercentage != null)
            'packet_loss_percentage': packetLossPercentage,
          if (audioCodec != null) 'audio_codec': audioCodec,
          if (networkType != null) 'network_type': networkType,
          if (hadConnectionIssues != null)
            'had_connection_issues': hadConnectionIssues,
          if (additionalMetrics != null) ...additionalMetrics,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      LogUtils.d('Voice call quality metrics tracked for $callType call',
          tag: _tag);
    } catch (e) {
      LogUtils.e('Failed to track voice call quality metrics: $e', tag: _tag);
    }
  }
}
