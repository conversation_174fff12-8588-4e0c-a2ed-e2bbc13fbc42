import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/firebase_message_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/location_service/location_service.dart';
import 'package:flutter_audio_room/services/permission_service/permission_service.dart';
import 'package:flutter_audio_room/shared/data/consts/sp_keys.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

/// Permission types that can be reminded
enum PermissionType {
  location,
  notification,
}

/// Permission reminder configuration
class PermissionReminderConfig {
  /// Minimum interval between reminders (in hours)
  final int reminderIntervalHours;

  /// Maximum number of reminders before giving up
  final int maxReminderCount;

  /// Delay after app launch before showing reminder (in seconds)
  final int delayAfterLaunchSeconds;

  const PermissionReminderConfig({
    this.reminderIntervalHours = 24, // 24 hours by default
    this.maxReminderCount = 5, // Maximum 5 reminders
    this.delayAfterLaunchSeconds = 3, // 3 seconds delay
  });
}

/// Service for managing permission reminders
class PermissionReminderService {
  final StorageService _storageService;
  final LocationService _locationService;
  final PermissionService _permissionService;
  final PermissionReminderConfig _config;

  static const String _tag = 'PermissionReminderService';

  PermissionReminderService({
    required StorageService storageService,
    required LocationService locationService,
    required PermissionService permissionService,
    PermissionReminderConfig? config,
  })  : _storageService = storageService,
        _locationService = locationService,
        _permissionService = permissionService,
        _config = config ?? const PermissionReminderConfig();

  /// Check if we should show permission reminder when app comes to foreground
  Future<void> checkAndShowPermissionReminder(BuildContext context) async {
    try {
      // Add delay to avoid showing dialog immediately after app launch
      await Future.delayed(Duration(seconds: _config.delayAfterLaunchSeconds));

      if (!context.mounted) return;

      // Check if we should show location permission reminder
      final shouldShowLocation =
          await _shouldShowPermissionReminder(PermissionType.location);
      if (shouldShowLocation && context.mounted) {
        await _showLocationPermissionReminder(context);
        return; // Only show one reminder at a time
      }

      // Check if we should show notification permission reminder
      final shouldShowNotification =
          await _shouldShowPermissionReminder(PermissionType.notification);
      if (shouldShowNotification && context.mounted) {
        await _showNotificationPermissionReminder(context);
      }
    } catch (e) {
      LogUtils.e('Error checking permission reminders: $e', tag: _tag);
    }
  }

  /// Record that a permission was denied
  Future<void> recordPermissionDenied(PermissionType type) async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      final currentCount = _getPermissionDeniedCount(type);

      switch (type) {
        case PermissionType.location:
          await _storageService.setInt(
              SPKeys.locationPermissionDeniedTime, now);
          await _storageService.setInt(
              SPKeys.locationPermissionDeniedCount, currentCount + 1);
          break;
        case PermissionType.notification:
          await _storageService.setInt(
              SPKeys.notificationPermissionDeniedTime, now);
          await _storageService.setInt(
              SPKeys.notificationPermissionDeniedCount, currentCount + 1);
          break;
      }

      LogUtils.d(
          'Recorded permission denied: $type, count: ${currentCount + 1}',
          tag: _tag);
    } catch (e) {
      LogUtils.e('Error recording permission denied: $e', tag: _tag);
    }
  }

  /// Clear permission denial records (called when permission is granted)
  Future<void> clearPermissionDenialRecord(PermissionType type) async {
    try {
      switch (type) {
        case PermissionType.location:
          await _storageService.remove(SPKeys.locationPermissionDeniedTime);
          await _storageService.remove(SPKeys.locationPermissionDeniedCount);
          break;
        case PermissionType.notification:
          await _storageService.remove(SPKeys.notificationPermissionDeniedTime);
          await _storageService
              .remove(SPKeys.notificationPermissionDeniedCount);
          break;
      }

      LogUtils.d('Cleared permission denial record: $type', tag: _tag);
    } catch (e) {
      LogUtils.e('Error clearing permission denial record: $e', tag: _tag);
    }
  }

  /// Check if we should show permission reminder
  Future<bool> _shouldShowPermissionReminder(PermissionType type) async {
    try {
      // Check if permission is already granted
      final isGranted = await _isPermissionGranted(type);
      if (isGranted) {
        // Clear denial records if permission is now granted
        await clearPermissionDenialRecord(type);
        return false;
      }

      // Check if we have denial records
      final deniedTime = _getPermissionDeniedTime(type);
      final deniedCount = _getPermissionDeniedCount(type);

      if (deniedTime == null || deniedCount == 0) {
        return false; // No denial records
      }

      // Check if we've exceeded max reminder count
      if (deniedCount > _config.maxReminderCount) {
        LogUtils.d('Max reminder count exceeded for $type', tag: _tag);
        return false;
      }

      // Check if enough time has passed since last denial
      final now = DateTime.now().millisecondsSinceEpoch;
      final timeSinceDenial = now - deniedTime;
      final reminderInterval =
          Duration(hours: _config.reminderIntervalHours).inMilliseconds;

      if (timeSinceDenial < reminderInterval) {
        return false; // Not enough time has passed
      }

      // Check if we've shown a reminder recently
      final lastReminderTime =
          _storageService.getInt(SPKeys.lastPermissionReminderTime);
      if (lastReminderTime != null) {
        final timeSinceLastReminder = now - lastReminderTime;
        final minTimeBetweenReminders = const Duration(hours: 1)
            .inMilliseconds; // At least 1 hour between any reminders

        if (timeSinceLastReminder < minTimeBetweenReminders) {
          return false;
        }
      }

      return true;
    } catch (e) {
      LogUtils.e('Error checking if should show permission reminder: $e',
          tag: _tag);
      return false;
    }
  }

  /// Check if permission is currently granted
  Future<bool> _isPermissionGranted(PermissionType type) async {
    try {
      switch (type) {
        case PermissionType.location:
          final result = await _locationService.isLocationPermissionGranted();
          return result.isRight() && (result.getRight() ?? false);
        case PermissionType.notification:
          // Check Firebase messaging permission
          return await FirebaseMessageUtils.requestPermission();
      }
    } catch (e) {
      LogUtils.e('Error checking permission status: $e', tag: _tag);
      return false;
    }
  }

  /// Get permission denied time
  int? _getPermissionDeniedTime(PermissionType type) {
    switch (type) {
      case PermissionType.location:
        return _storageService.getInt(SPKeys.locationPermissionDeniedTime);
      case PermissionType.notification:
        return _storageService.getInt(SPKeys.notificationPermissionDeniedTime);
    }
  }

  /// Get permission denied count
  int _getPermissionDeniedCount(PermissionType type) {
    switch (type) {
      case PermissionType.location:
        return _storageService.getInt(SPKeys.locationPermissionDeniedCount) ??
            0;
      case PermissionType.notification:
        return _storageService
                .getInt(SPKeys.notificationPermissionDeniedCount) ??
            0;
    }
  }

  /// Show location permission reminder dialog
  Future<void> _showLocationPermissionReminder(BuildContext context) async {
    await _updateLastReminderTime();

    if (!context.mounted) return;

    final result = await context.showOkCancelAlertDialog(
      title: 'Need location permission',
      content:
          'To provide you with better services, we need to access your location information. Please enable location permissions in the settings.',
      confirmText: 'Go to settings',
      cancelText: 'Later',
      dialogTag: 'location_permission_reminder',
    );

    if (result == true && context.mounted) {
      await _handleLocationPermissionRequest(context);
    }
  }

  /// Show notification permission reminder dialog
  Future<void> _showNotificationPermissionReminder(BuildContext context) async {
    await _updateLastReminderTime();

    if (!context.mounted) return;

    final result = await context.showOkCancelAlertDialog(
      title: 'Need notification permission',
      content:
          'To notify you of important messages, we need to send notifications. Please enable notification permissions in the settings.',
      confirmText: 'Go to settings',
      cancelText: 'Later',
      dialogTag: 'notification_permission_reminder',
    );

    if (result == true && context.mounted) {
      await _handleNotificationPermissionRequest(context);
    }
  }

  /// Handle location permission request
  Future<void> _handleLocationPermissionRequest(BuildContext context) async {
    try {
      final permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.deniedForever) {
        // Open settings for permanently denied permission
        await Geolocator.openLocationSettings();
      } else {
        // Try to request permission
        final result = await _locationService.requestLocationPermission(
            autoOpenSettings: false);
        result.fold(
          (error) {
            if (error.identifier == 'LOCATION_PERMISSION_DENIED_FOREVER') {
              // Open settings if permanently denied
              Geolocator.openLocationSettings();
            } else {
              // Record denial for temporary denial
              recordPermissionDenied(PermissionType.location);
            }
          },
          (granted) {
            if (granted) {
              // Clear denial records on success
              clearPermissionDenialRecord(PermissionType.location);
            }
          },
        );
      }
    } catch (e) {
      LogUtils.e('Error handling location permission request: $e', tag: _tag);
    }
  }

  /// Handle notification permission request
  Future<void> _handleNotificationPermissionRequest(
      BuildContext context) async {
    try {
      final granted = await FirebaseMessageUtils.requestPermission();

      if (granted) {
        // Clear denial records on success
        await clearPermissionDenialRecord(PermissionType.notification);
      } else {
        // Check if permanently denied and open settings
        final status = await Permission.notification.status;
        if (status.isPermanentlyDenied) {
          await _permissionService.openSettings();
        } else {
          // Record denial for temporary denial
          await recordPermissionDenied(PermissionType.notification);
        }
      }
    } catch (e) {
      LogUtils.e('Error handling notification permission request: $e',
          tag: _tag);
    }
  }

  /// Update last reminder time
  Future<void> _updateLastReminderTime() async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      await _storageService.setInt(SPKeys.lastPermissionReminderTime, now);
    } catch (e) {
      LogUtils.e('Error updating last reminder time: $e', tag: _tag);
    }
  }
}
