name: flutter_audio_room
description: "A new Flutter project."

# Prevent accidental publishing to pub.dev.
publish_to: 'none'

version: 1.0.0+6

environment:
  sdk: ^3.5.4

dependency_overrides:
  # flutter_cache_manager conflicts with svgaplayer_flutter
  http: ^0.13.3

dependencies:
  flutter:
      sdk: flutter
  flutter_localizations:
    sdk: flutter
  # code generation
  json_annotation: ^4.9.0
  freezed_annotation: ^2.4.4
  riverpod_annotation: ^2.3.5
  equatable: ^2.0.7
  flutter_gen_core: 5.3.1

  # WebRTC
  flutter_webrtc: ^0.12.8

  # device configuration
  package_info_plus: ^8.1.1
  device_info_plus: ^10.1.2
  android_id: ^0.4.0

  # network
  connectivity_plus: ^6.1.0
  dio: ^5.7.0
  dart_ipify: ^1.1.1

  socket_io_client: ^3.1.1

  # agora
  agora_rtc_engine: ^6.5.0
  agora_rtm: ^2.2.2

  # state management
  flutter_riverpod: ^2.6.1

  # dependency injection
  get_it: ^8.0.2

  # storage
  shared_preferences: ^2.3.3
  flutter_secure_storage: ^9.2.2
  hive_flutter: ^1.1.0
  isar:
    hosted: https://pub.isar-community.dev
    version: ^4.0.3
  isar_flutter_libs:
    hosted: https://pub.isar-community.dev
    version: ^4.0.3

  # chat
  flutter_chat_types: ^3.6.2

  # ui
  flutter_animate: ^4.5.2
  pinput: ^5.0.0  #input otp
  flutter_keyboard_visibility: ^6.0.0
  flutter_screenutil: ^5.9.3
  flutter_easyloading: ^3.0.5
  modal_bottom_sheet: ^3.0.0
  cupertino_icons: ^1.0.8
  avatar_glow: ^3.0.1
  ripple_wave: ^0.1.4
  smooth_page_indicator: ^1.2.0+3
  infinite_scroll_pagination: ^4.1.0
  shimmer: ^3.0.0
  dismissible_page: ^1.0.2
  flutter_slidable: ^3.1.2
  pull_to_refresh: ^2.0.0
  flutter_context_menu: ^0.2.2
  super_context_menu: ^0.8.24
  macos_ui: 2.0.0
  adaptive_dialog: ^2.2.1
  flutter_staggered_grid_view: ^0.7.0

  # internationalization
  intl: ^0.19.0

  # utils
  rxdart: ^0.28.0   # extension for streams
  easy_debounce: ^2.0.3
  uuid: ^4.5.1
  screen_protector: ^1.4.2+1
  flutter_cache_manager: ^3.4.1
  path_provider: ^2.1.5
  path: ^1.9.0
  flutter_timezone: ^3.0.1
  logger: ^2.5.0
  synchronized: ^3.1.0+1
  share_plus: ^10.1.4
  intl_phone_number_input: ^0.7.4

  # crypto
  pointycastle: ^3.9.1
  encrypt: ^5.0.3
  libsignal_protocol_dart: ^0.7.1

  # permission handler
  permission_handler: ^11.3.1

  # Location
  geolocator: ^10.1.0
  geocoding: ^3.0.0

  # media
  image_picker: ^1.1.2
  video_thumbnail: ^0.5.3
  just_audio: ^0.9.36
  photo_view: ^0.15.0
  file_picker: ^8.1.7
  video_player: ^2.9.2
  svgaplayer_flutter: ^2.2.0
  cached_network_image: ^3.3.0
  flutter_sound: ^9.27.0
  audio_session: ^0.1.23
  flutter_image_compress: ^2.4.0
  video_compress: ^3.1.4
  flutter_svg: ^2.0.7

  # app config
  flutter_native_splash: ^2.4.4
  flutter_launcher_icons: ^0.14.3
  flutter_flavorizr: ^2.2.0

  in_app_purchase: ^3.2.2
  in_app_purchase_storekit: ^0.3.22+1
  in_app_purchase_android: ^0.4.0+1

  # firebase
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  firebase_crashlytics: ^4.3.7
  firebase_analytics: ^11.5.0

  #notification
  flutter_local_notifications: ^19.2.0
  app_badge_plus: ^1.2.3

  url_launcher: ^6.3.1
  scrollable_positioned_list: ^0.3.8
  dio_smart_retry: ^7.0.1
  app_tracking_transparency: ^2.0.6+1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0
  flutter_gen_runner:
  build_runner:
  freezed: ^2.4.7
  http_mock_adapter: ^0.6.1
  json_serializable: ^6.8.0
  mocktail: ^1.0.4
  test_coverage_badge: ^0.3.2
  riverpod_generator: ^2.3.11

flutter_gen:
  output: lib/gen/ # Optional (default: lib/gen/)
  line_length: 80 # Optional (default: 80)

  # Optional
  integrations:
    flutter_svg: true
    flare_flutter: true
    rive: true
    lottie: true

  fonts:
    enable: true

flutter:
  uses-material-design: true
  # Enable generation of localized Strings from arb files.
  generate: true

  assets:
    # Add assets from the images directory to the application.
    - assets/images/
    - assets/audio/
    - assets/svgs/

  fonts:
    - family: Inter
      fonts:
        - asset: fonts/Inter/Inter-Thin.ttf
          weight: 100
        - asset: fonts/Inter/Inter-ExtraLight.ttf
          weight: 200
        - asset: fonts/Inter/Inter-Light.ttf
          weight: 300
        - asset: fonts/Inter/Inter-Regular.ttf
          weight: 400
        - asset: fonts/Inter/Inter-Medium.ttf
          weight: 500
        - asset: fonts/Inter/Inter-SemiBold.ttf
          weight: 600
        - asset: fonts/Inter/Inter-Bold.ttf
          weight: 700
        - asset: fonts/Inter/Inter-ExtraBold.ttf
          weight: 800
        - asset: fonts/Inter/Inter-Black.ttf
          weight: 900

